name: Deploy Node.js App with PM2

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Test SSH Connection
        env:
          SERVER_USER: ${{ secrets.SERVER_USER }}
          SERVER_IP: ${{ secrets.SERVER_IP }}
        run: |
          echo "Testing SSH connection to server using username '${SERVER_USER}'"
          ssh -o StrictHostKeyChecking=no ${SERVER_USER}@${SERVER_IP} -p 22 "echo 'Connected as $(whoami)'"

      - name: Deploy to Production Server
        uses: appleboy/ssh-action@v0.1.8
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SERVER_USER }}
          debug: true
          password: ${{ secrets.SERVER_PASSWORD }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /home/<USER>/capacitybay-certification-portal

            # Pull latest code
            git pull origin master

            # Install/update dependencies
            npm install

            # Build the application
            npm run build

            # Install PM2 if not already installed
            if ! command -v pm2 &> /dev/null; then
              npm install -g pm2
            fi

            # Create PM2 ecosystem file if it doesn't exist
            if [ ! -f ecosystem.config.js ]; then
              cat > ecosystem.config.js << 'EOF'
            module.exports = {
              apps: [{
                name: 'certification-portal',
                script: 'npm',
                args: 'start',
                cwd: '/home/<USER>/capacitybay-certification-portal/',
                env: {
                  NODE_ENV: 'production',
                  PORT: 3700
                },
                instances: 1,
                autorestart: true,
                watch: false,
                max_memory_restart: '1G',
                error_file: '/home/<USER>/capacitybay-certification-portal/logs/err.log',
                out_file: '/home/<USER>/capacitybay-certification-portal/logs/out.log',
                log_file: '/home/<USER>/capacitybay-certification-portal/logs/combined.log',
                time: true
              }]
            }
            EOF
            fi

            # Create logs directory
            mkdir -p logs

            # Stop existing PM2 process if running
            pm2 stop certification-portal || true
            pm2 delete certification-portal || true

            # Start the application with PM2
            pm2 start ecosystem.config.js

            # Save PM2 configuration and setup startup script
            pm2 save
            pm2 startup systemd -u callu --hp /home/<USER>

            # Show PM2 status
            pm2 status
