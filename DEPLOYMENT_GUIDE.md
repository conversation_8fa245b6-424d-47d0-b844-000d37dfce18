# 🚀 Deployment Guide - Certificate PDF Generation

## ✅ Issues Fixed

### 1. Images Now Working
- ✅ **Logo**: `capalogo.png` now loads correctly
- ✅ **Signature**: `signature.png` now loads correctly  
- ✅ **QR Code**: Generated dynamically and embedded

### 2. Environment Configuration

#### For Local Development (.env.local):
```env
CHROMIUM_LOCAL_EXEC_PATH=/Applications/Google Chrome.app/Contents/MacOS/Google Chrome
```

#### For Vercel Production (vercel.json):
```json
{
  "env": {
    "CHROMIUM_REMOTE_EXEC_PATH": "https://github.com/Sparticuz/chromium/releases/download/v133.0.0/chromium-v133.0.0-pack.tar"
  }
}
```

## 🔧 How Browser Detection Works

The system automatically detects the environment:

```typescript
async function getBrowser() {
  const REMOTE_PATH = process.env.CHROMIUM_REMOTE_EXEC_PATH;
  const LOCAL_PATH = process.env.CHROMIUM_LOCAL_EXEC_PATH;
  
  // 1. Vercel Production: Use remote executable
  if (!!REMOTE_PATH) {
    console.log("Using remote Chromium executable for Vercel");
    return await puppeteerCore.launch({
      args: chromium.args,
      executablePath: await chromium.executablePath(REMOTE_PATH),
      defaultViewport: null,
      headless: true,
    });
  }

  // 2. Local Development: Use local Chrome
  if (!!LOCAL_PATH) {
    console.log("Using local Chromium executable:", LOCAL_PATH);
    return await puppeteerCore.launch({
      executablePath: LOCAL_PATH,
      defaultViewport: null,
      headless: true,
    });
  }

  // 3. Fallback: Auto-detection
  console.log("Using puppeteer auto-detection for Chrome");
  const puppeteer = await import('puppeteer');
  return await puppeteer.default.launch({ headless: true });
}
```

## 📋 Deployment Steps for Vercel

### 1. No Additional Environment Variables Needed
- ✅ `CHROMIUM_REMOTE_EXEC_PATH` is already set in `vercel.json`
- ❌ **Do NOT set** `CHROMIUM_LOCAL_EXEC_PATH` on Vercel
- ❌ **Do NOT add** any Chrome-related environment variables to Vercel dashboard

### 2. Deploy to Vercel
```bash
# Option 1: Deploy via Vercel CLI
vercel --prod

# Option 2: Deploy via Git (if connected to GitHub)
git add .
git commit -m "Fixed certificate PDF generation with images"
git push origin main
```

### 3. Verify Deployment
1. **Check build logs** for any errors
2. **Test certificate download** at your URL:
   ```
   https://capacitybay-certification-portal.vercel.app/api/certificates/download/[certificate-id]
   ```
3. **Verify images appear** in the generated PDF:
   - Logo in header
   - Signature in footer
   - QR code for verification

## 🎯 What's Different Now

### Images Fixed:
- **Logo**: Loads from `public/capalogo.png` and converts to base64
- **Signature**: Loads from `public/signature.png` and converts to base64
- **QR Code**: Generated dynamically with verification URL

### Environment Detection:
- **Vercel**: Automatically uses remote Chromium executable
- **Local**: Uses your Mac's Chrome installation
- **Fallback**: Puppeteer auto-detection if paths fail

### No Manual Configuration:
- **Vercel**: Environment variables set automatically via `vercel.json`
- **Local**: Environment variables set in `.env.local`
- **Robust**: Multiple fallback options for browser detection

## 🔍 Troubleshooting

### If Images Still Don't Show:
1. **Check file paths**: Ensure `public/capalogo.png` and `public/signature.png` exist
2. **Check console logs**: Look for "Could not load logo/signature file" warnings
3. **Verify base64 encoding**: Images should be converted to base64 data URLs

### If Browser Not Found:
1. **Local**: Update `CHROMIUM_LOCAL_EXEC_PATH` in `.env.local` to your Chrome path
2. **Vercel**: Check that `CHROMIUM_REMOTE_EXEC_PATH` is set in `vercel.json`
3. **Fallback**: The system will try puppeteer auto-detection

### If PDF Generation Fails:
1. **Check memory limits**: Vercel functions have 1024MB memory limit
2. **Check timeout**: Functions have 30-second timeout
3. **Check logs**: Monitor Vercel function logs for specific errors

## ✅ Ready for Production

Your certificate system is now ready with:
- ✅ **Professional HTML template** with Google Fonts
- ✅ **Working images** (logo, signature, QR code)
- ✅ **Vercel-compatible** browser detection
- ✅ **Robust fallbacks** for different environments
- ✅ **Community-tested approach** for reliable deployment

**Deploy to Vercel and your certificates will work perfectly!** 🎉
