# 📧 Email Setup Guide for Certificate Delivery

## 🚀 Quick Setup (Gmail - Recommended)

### Step 1: Enable 2-Factor Authentication
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click **Security** in the left sidebar
3. Under "Signing in to Google", click **2-Step Verification**
4. Follow the setup process to enable 2FA

### Step 2: Generate App Password
1. In Google Account Settings, go to **Security**
2. Under "Signing in to Google", click **App passwords**
3. Select **Mail** as the app
4. Select **Other (Custom name)** as the device
5. Enter "CapacityBay Certificate System" as the name
6. Click **Generate**
7. **Copy the 16-character password** (you'll need this for Step 3)

### Step 3: Update Environment Variables
Edit your `.env.local` file and replace the placeholder values:

```env
# Email Configuration for Certificate Delivery
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password
SMTP_FROM=<EMAIL>
```

**Example:**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=abcd efgh ijkl mnop
SMTP_FROM=<EMAIL>
```

### Step 4: Restart Development Server
```bash
# Stop the current server (Ctrl+C)
npm run dev
```

### Step 5: Test Email Sending
1. Go to Admin Dashboard → Issue Certificate
2. Issue a certificate to a test student
3. Check the terminal logs for success messages
4. Verify the student receives the email with PDF attachment

## 🔧 Alternative Email Providers

### Outlook/Hotmail
```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
```

### Yahoo Mail
```env
SMTP_HOST=smtp.mail.yahoo.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### Custom SMTP Server
```env
SMTP_HOST=your-smtp-server.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your-username
SMTP_PASS=your-password
```

## 🐛 Troubleshooting

### Common Errors and Solutions

#### "Greeting never received" / ETIMEDOUT
- **Cause**: Wrong SMTP credentials or network issues
- **Solution**: Double-check your email and app password

#### "Authentication failed" / EAUTH
- **Cause**: Incorrect username or password
- **Solution**: Verify SMTP_USER and SMTP_PASS are correct

#### "Email service not configured"
- **Cause**: Missing or placeholder credentials in .env.local
- **Solution**: Update .env.local with real credentials and restart server

### Verification Steps
1. Check terminal logs for "✅ Email service configured successfully"
2. Look for "✅ Certificate email sent successfully" when issuing certificates
3. Verify emails arrive in student's inbox (check spam folder)

## 📋 Email Features

### What Students Receive
- **Subject**: "🎉 Your Certificate is Ready - [Course Name]"
- **Content**: Personalized email with certificate details
- **Attachment**: High-quality PDF certificate
- **Verification**: Direct link to verify certificate online
- **Branding**: CapacityBay branding and social media links

### Email Template Features
- ✅ Modern, responsive design
- ✅ White background with green brand colors
- ✅ Certificate details (ID, course, date)
- ✅ Verification instructions
- ✅ Social media integration
- ✅ Mobile-friendly layout

## 🔒 Security Notes

- **Never commit real credentials** to version control
- **Use App Passwords** instead of regular passwords for Gmail
- **Keep .env.local** in your .gitignore file
- **Use environment variables** for production deployment

## 📞 Support

If you encounter issues:
1. Check the terminal logs for specific error messages
2. Verify your email provider's SMTP settings
3. Test with a simple email client first
4. Contact your email provider for SMTP support

---

**Note**: The system works perfectly without email configuration - certificates are still created and can be downloaded manually. Email is an optional enhancement for better user experience.
