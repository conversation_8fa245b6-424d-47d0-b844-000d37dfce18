# Legacy Certificate Migration Guide

This guide explains how to migrate your existing certificates from the old system to the new certificate verification system while maintaining backward compatibility.

## Overview

Your new system now supports **multiple certificate ID formats**:

- **Legacy certificates (24-char)**: MongoDB ObjectIds like `655392de2452d72f49b83e31`
- **Legacy certificates (32-char)**: Hex strings like `eacbebc81e1e4c96829b185021488352`
- **New certificates**: UUIDs like `550e8400-e29b-41d4-a716-************`

## Backward Compatibility

✅ **Old verification URLs will continue to work:**
- `https://certverify.capacitybay.org/655392de2452d72f49b83e31` → Works (24-char legacy)
- `https://certverify.capacitybay.org/eacbebc81e1e4c96829b185021488352` → Works (32-char legacy)
- `https://certverify.capacitybay.org/550e8400-e29b-41d4-a716-************` → Works (new format)

✅ **Root-level URLs (old format) automatically redirect:**
- `https://certverify.capacitybay.org/eacbebc81e1e4c96829b185021488352` → Redirects to `/verify/eacbebc81e1e4c96829b185021488352`

✅ **Both formats work for:**
- Certificate verification (`/verify/[id]`)
- Certificate downloads (`/api/certificates/download/[id]`)
- API verification (`/api/verify/[id]`)

## Migration Process

### Step 1: Prepare Your Legacy Data

Create a data structure with your existing certificates:

```typescript
interface LegacyCertificateData {
  _id: string; // Original certificate ID (24-char MongoDB ObjectId or 32-char hex)
  studentName: string;
  studentEmail: string;
  courseName: string;
  courseDescription?: string;
  courseDuration?: string;
  courseBadgeImage?: string;
  issueDate: string | Date;
  downloadCount?: number;
  shareCount?: number;
  isRevoked?: boolean;
}
```

### Step 2: Update Migration Script

Edit `src/scripts/migrate-legacy-certificates.ts` and replace the example data with your actual certificate data:

```typescript
const legacyData: LegacyMigrationData = {
  certificates: [
    {
      _id: '655392de2452d72f49b83e31',
      studentName: 'John Doe',
      studentEmail: '<EMAIL>',
      courseName: 'Web Development Fundamentals',
      courseDescription: 'Learn the basics of web development',
      courseDuration: '8 weeks',
      courseBadgeImage: '/badges/web-dev.png',
      issueDate: '2023-11-15',
      downloadCount: 5,
      shareCount: 2,
      isRevoked: false
    },
    // Add all your certificates here...
  ]
};
```

### Step 3: Run Migration

```bash
# Run the migration script
npm run migrate-legacy
```

The script will:
- ✅ Create missing students with temporary passwords
- ✅ Create missing courses
- ✅ Import certificates with original MongoDB ObjectIds
- ✅ Mark certificates as legacy (`isLegacy: true`)
- ✅ Maintain all existing verification URLs

### Step 4: Test Migration

```bash
# Create test certificates for testing
npm run test-legacy

# Start development server
npm run dev

# Test URLs in browser:
# http://localhost:3000/verify/655392de2452d72f49b83e31 (legacy)
# http://localhost:3000/verify/[new-uuid] (new format)
```

## Database Schema Changes

### Certificate Model Updates

```typescript
interface ICertificate {
  studentId: mongoose.Types.ObjectId;
  courseId: mongoose.Types.ObjectId;
  issueDate: Date;
  certificateId?: string; // Optional for legacy certificates
  downloadCount: number;
  shareCount: number;
  isRevoked: boolean;
  isLegacy?: boolean; // Flag for legacy certificates
  createdAt: Date;
  updatedAt: Date;
}
```

### Key Changes:
- `certificateId` is now optional (legacy certificates don't have UUIDs)
- Added `isLegacy` flag to identify legacy certificates
- Legacy certificates use their MongoDB `_id` for verification

## API Behavior

### Verification API (`/api/verify/[id]`)

The API automatically detects the ID format:

```typescript
// Legacy ID (24-char hex) → Search by _id
if (isValidObjectId(certificateId)) {
  certificate = await Certificate.findOne({ _id: certificateId, isRevoked: false });
}
// New ID (UUID) → Search by certificateId
else {
  certificate = await Certificate.findOne({ certificateId: certificateId, isRevoked: false });
}
```

### Response Format

```json
{
  "verified": true,
  "certificate": {
    "id": "655392de2452d72f49b83e31", // Original ID or UUID
    "issueDate": "2023-11-15T00:00:00.000Z",
    "downloadCount": 5,
    "shareCount": 2
  },
  "student": { "name": "John Doe", "email": "<EMAIL>" },
  "course": { "title": "Web Development", "description": "..." },
  "isLegacy": true // Indicates if this is a legacy certificate
}
```

## Post-Migration Tasks

### 1. Student Account Setup
- Migrated students get temporary passwords
- Send password reset emails to all migrated students
- Students can use "Forgot Password" to set their own passwords

### 2. Course Information Updates
- Review and update course descriptions
- Upload proper badge images
- Set correct course durations

### 3. Testing Checklist
- [ ] Legacy verification URLs work
- [ ] New certificate creation works
- [ ] Certificate downloads work for both formats
- [ ] Email notifications work
- [ ] Student login works
- [ ] Admin panel shows all certificates

## Troubleshooting

### Common Issues

**Q: Legacy URL returns "Certificate not found"**
A: Check that the certificate was properly migrated with the correct MongoDB ObjectId

**Q: Student can't log in**
A: Migrated students need to use password reset to set their passwords

**Q: Course information is incomplete**
A: Update course details through the admin panel after migration

**Q: Email notifications not working**
A: Check SMTP configuration in `.env.local`

### Verification Commands

```bash
# Check if certificate exists in database
mongo
use capacertverify
db.certificates.findOne({_id: ObjectId("655392de2452d72f49b83e31")})

# Check migration status
db.certificates.find({isLegacy: true}).count()
```

## Support

If you encounter issues during migration:

1. Check the migration logs for specific error messages
2. Verify your data format matches the expected structure
3. Test with a small batch of certificates first
4. Ensure your MongoDB connection is working properly

The system is designed to be fully backward compatible, so your existing certificate links will continue to work seamlessly with the new system.
