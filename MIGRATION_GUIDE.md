# Certificate Migration Guide

This guide will help you migrate your existing certificate data from `certDB.certifiedusers.json` to the new certificate verification system.

## 🚀 Quick Start

### Step 1: Prepare Your Data
1. Ensure `certDB.certifiedusers.json` is in the project root directory
2. The file should contain your existing student and certificate data

### Step 2: Run the Migration
```bash
npm run migrate-legacy
```

### Step 3: Test the Migration
```bash
# Create test certificates for verification
npm run test-legacy

# Start the development server
npm run dev

# Test URLs in your browser
```

## 📊 What the Migration Does

### ✅ **Students**
- Creates student accounts for all users in your JSON data
- Sets password to `CapacityBay@123` for all students
- Uses existing email addresses and names

### ✅ **Courses**
- Automatically creates courses found in your data:
  - HTML, CSS, JavaScript, React.js, Node.js
  - Python, GitHub, Bootstrap, Cyber Security
  - Internship, Backend JavaScript
- Adds proper descriptions and durations
- Sets appropriate badge images

### ✅ **Certificates**
- Preserves original MongoDB ObjectIds
- Maintains all certificate relationships
- Sets legacy flag for backward compatibility
- Preserves issue dates from original data

## 🔗 **URL Compatibility**

After migration, these URLs will work:

**Verification URLs:**
- `http://localhost:3000/verify/631f854a9b27e97f9ebce5b5` ✅
- `http://localhost:3000/verify/631f854a9b27e97f9ebce5b3` ✅

**Root-level URLs (old format):**
- `http://localhost:3000/631f854a9b27e97f9ebce5b5` ✅ (redirects to verification)
- `http://localhost:3000/631f854a9b27e97f9ebce5b3` ✅ (redirects to verification)

**Download URLs:**
- `http://localhost:3000/api/certificates/download/631f854a9b27e97f9ebce5b5` ✅

## 👥 **Student Login**

After migration, students can login with:
- **Email**: Their existing email address
- **Password**: `CapacityBay@123`

Students can change their password after logging in.

## 📋 **Sample Data from Your File**

Your JSON contains students like:
```json
{
  "_id": {"$oid": "631f854a9b27e97f9ebce5b5"},
  "name": "Franklin Azodo",
  "email": "<EMAIL>",
  "course": [
    {
      "courseTitle": "HTML",
      "date": "September 12th 2022",
      "_id": {"$oid": "631f854a9b27e97f9ebce5b3"}
    }
  ]
}
```

This will create:
- ✅ Student: Franklin Azodo (<EMAIL>)
- ✅ Course: HTML (if not exists)
- ✅ Certificate: ID `631f854a9b27e97f9ebce5b3`

## 🧪 **Testing**

### Test URLs
```bash
# Test with real IDs from your data
http://localhost:3000/631f854a9b27e97f9ebce5b5  # Franklin's student ID
http://localhost:3000/631f854a9b27e97f9ebce5b3  # Franklin's HTML certificate
```

### Test Login
1. Go to `http://localhost:3000/login`
2. Email: `<EMAIL>`
3. Password: `CapacityBay@123`

## 📈 **Migration Statistics**

The script will show:
- Number of students created
- Number of courses processed
- Number of certificates migrated
- Any errors or skipped items

## ⚠️ **Important Notes**

1. **Backup First**: Always backup your database before migration
2. **Run Once**: The script checks for duplicates, but it's best to run once
3. **Password**: All students get the same initial password
4. **Course Updates**: You can update course descriptions later via admin panel
5. **Legacy Support**: Old URLs continue to work seamlessly

## 🔧 **Troubleshooting**

**Q: Migration fails with "JSON file not found"**
A: Ensure `certDB.certifiedusers.json` is in the project root directory

**Q: Some certificates don't verify**
A: Check the migration logs for any errors during certificate creation

**Q: Student can't login**
A: Verify the email address and use password: `CapacityBay@123`

**Q: Old URLs don't work**
A: Make sure you've restarted the development server after migration

## 🎉 **Success!**

After successful migration:
- All your existing certificate URLs will work
- Students can login and view their certificates
- New certificates continue to work with UUID format
- Admin panel shows all migrated data

Your certificate verification system is now fully backward compatible! 🚀
