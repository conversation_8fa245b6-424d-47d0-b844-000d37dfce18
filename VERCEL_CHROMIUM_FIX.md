# Vercel Chromium Configuration Fix - FINAL SOLUTION

## Problem
The certificate download functionality was failing on Vercel with the error:
```
"The input directory "/var/task/node_modules/@sparticuz/chromium/bin" does not exist. Please provide the location of the brotli files."
```

## Root Cause
The `@sparticuz/chromium` package was not properly configured for the Vercel serverless environment. The issue was with environment detection and Next.js external package configuration.

## Final Working Solution

### 1. Updated Next.js Configuration (`next.config.ts`)
```typescript
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  serverExternalPackages: [
    'puppeteer-core',
    '@sparticuz/chromium'
  ],
};

export default nextConfig;
```

### 2. Updated PDF Generators with Correct Environment Detection
Key change: Use `process.env.VERCEL_ENV === "production"` instead of `process.env.VERCEL === '1'`

```typescript
if (process.env.VERCEL_ENV === "production") {
  // Use Chromium for Vercel serverless environment
  const executablePath = await chromium.executablePath();

  browser = await puppeteerCore.launch({
    args: chromium.args,
    executablePath,
    headless: true,
  });
} else {
  // Use local Puppeteer for development
  browser = await puppeteer.launch({
    headless: true,
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--single-process',
      '--disable-gpu'
    ]
  });
}
```

### 3. Updated Vercel Configuration (`vercel.json`)
- Increased function memory to 1024MB for PDF generation
- Set `PUPPETEER_SKIP_CHROMIUM_DOWNLOAD` to `true`

### 4. Updated Package Scripts (`package.json`)
- Removed Chrome installation from `vercel-build` script
- @sparticuz/chromium provides its own Chromium binary

## Key Configuration Changes

### Browser Launch Arguments for Vercel:
```javascript
args: [
  ...chromium.args,
  '--no-sandbox',
  '--disable-setuid-sandbox',
  '--disable-dev-shm-usage',
  '--disable-accelerated-2d-canvas',
  '--no-first-run',
  '--no-zygote',
  '--single-process',
  '--disable-gpu',
  '--disable-web-security',
  '--disable-features=VizDisplayCompositor'
]
```

### Environment Detection:
```javascript
const isVercel = process.env.VERCEL === '1';
```

## Testing

### Local Testing
Run the test script to verify certificate generation works locally:
```bash
node test-certificate-vercel.js
```

### Vercel Deployment
1. Deploy to Vercel
2. Test certificate download functionality
3. Monitor function logs for any errors

## Dependencies
- `@sparticuz/chromium@^137.0.1` - Compatible with Puppeteer v24.10.2
- `puppeteer-core@^24.10.2` - For serverless environments
- `puppeteer@^24.10.2` - For local development

## Memory Requirements
- Vercel function memory: 1024MB (configured in vercel.json)
- Function timeout: 30 seconds (should be sufficient for PDF generation)

## Troubleshooting

### If certificate download still fails:
1. Check Vercel function logs for specific error messages
2. Verify environment variables are set correctly
3. Ensure the function has sufficient memory allocation
4. Check that @sparticuz/chromium version is compatible with puppeteer version

### Common Issues:
- **Memory errors**: Increase function memory in vercel.json
- **Timeout errors**: Increase maxDuration in vercel.json
- **Binary not found**: Ensure PUPPETEER_SKIP_CHROMIUM_DOWNLOAD is set to true

## Next Steps
1. Deploy the changes to Vercel
2. Test certificate download functionality
3. Monitor performance and adjust memory allocation if needed
4. Consider implementing certificate caching for better performance
