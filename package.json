{"name": "modern-cert-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "npm run install-chrome", "install-chrome": "npx puppeteer browsers install chrome", "vercel-build": "next build", "create-accounts": "ts-node -r tsconfig-paths/register -P tsconfig.scripts.json src/scripts/create-test-accounts.ts", "reset-db": "ts-node -r tsconfig-paths/register -P tsconfig.scripts.json src/scripts/reset-database.ts", "seed-db": "ts-node -r tsconfig-paths/register -P tsconfig.scripts.json src/scripts/seed-database.ts", "migrate-legacy": "ts-node -r tsconfig-paths/register -P tsconfig.scripts.json src/scripts/migrate-legacy-certificates.ts", "test-legacy": "ts-node -r tsconfig-paths/register -P tsconfig.scripts.json src/scripts/test-legacy-verification.ts", "test-dates": "ts-node src/scripts/test-date-parsing.ts", "fix-indexes": "ts-node -r tsconfig-paths/register -P tsconfig.scripts.json src/scripts/fix-database-indexes.ts"}, "dependencies": {"@sparticuz/chromium": "^137.0.1", "@tailwindcss/line-clamp": "^0.4.4", "@types/jsdom": "^21.1.7", "bcryptjs": "^3.0.2", "canvas": "^3.1.1", "html2canvas": "^1.4.1", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "moment": "^2.30.1", "mongoose": "^8.15.1", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "pdfkit": "^0.17.1", "puppeteer": "^24.10.2", "puppeteer-core": "^24.10.2", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.13.9", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.3", "postcss": "^8.5.4", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5"}, "config": {"puppeteer": {"skipDownload": false, "args": ["--no-sandbox", "--disable-setuid-sandbox", "--disable-dev-shm-usage", "--disable-accelerated-2d-canvas", "--no-first-run", "--no-zygote", "--single-process", "--disable-gpu"]}}, "puppeteer": {"skipDownload": false}}