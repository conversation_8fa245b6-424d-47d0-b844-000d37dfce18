'use client';

import { useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { notFound } from 'next/navigation';

// Helper function to check if a string looks like a certificate ID
function isValidCertificateId(id: string): boolean {
  // Check for MongoDB ObjectId (24 chars), 32-char hex, or UUID format
  return /^[a-fA-F0-9]{24}$/.test(id) || 
         /^[a-fA-F0-9]{32}$/.test(id) || 
         /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
}

// List of known routes that should NOT be treated as certificate IDs
const RESERVED_ROUTES = [
  'admin',
  'api',
  'courses',
  'forgot-password',
  'login',
  'privacy',
  'reset-password',
  'security',
  'student',
  'terms',
  'test',
  'verify',
  '_next',
  'favicon.ico',
  'robots.txt',
  'sitemap.xml'
];

export default function CertificateRedirectPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  const router = useRouter();
  const id = resolvedParams.id;

  useEffect(() => {
    // Check if this is a reserved route
    if (RESERVED_ROUTES.includes(id.toLowerCase())) {
      notFound();
      return;
    }

    // Check if the ID looks like a certificate ID
    if (isValidCertificateId(id)) {
      // Redirect to the verification page
      router.replace(`/verify/${id}`);
    } else {
      // If it doesn't look like a certificate ID, show 404
      notFound();
    }
  }, [id, router]);

  // Show loading state while redirecting
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-emerald-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-400 mx-auto mb-4"></div>
        <p className="text-white text-lg">Verifying certificate...</p>
        <p className="text-gray-300 text-sm mt-2">Redirecting to verification page</p>
      </div>
    </div>
  );
}
