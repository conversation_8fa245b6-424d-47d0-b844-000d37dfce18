'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { IStudent, ICourse } from '@/lib/db/models';
import ModernAdminLayout from '@/components/admin/ModernAdminLayout';
import SearchableSelect from '@/components/ui/SearchableSelect';

type StudentData = Omit<IStudent, 'password'> & { _id: string };
type CourseData = ICourse & { _id: string };

export default function IssueCertificate() {
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/admin/login');
    },
  });
  const router = useRouter();
  
  const [students, setStudents] = useState<StudentData[]>([]);
  const [courses, setCourses] = useState<CourseData[]>([]);
  const [selectedStudent, setSelectedStudent] = useState<string>('');
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [issueDate, setIssueDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    if (status !== 'authenticated' || !session) return;
    
    if (session.user?.role !== 'admin') {
      router.push('/admin/login');
      return;
    }

    fetchData();
  }, [session, status, router]);

  const fetchData = async () => {
    try {
      // Fetch students
      const studentsResponse = await fetch('/api/students');
      if (studentsResponse.ok) {
        const studentsData = await studentsResponse.json();
        setStudents(studentsData.students || studentsData);
      }

      // Fetch courses
      const coursesResponse = await fetch('/api/courses?active=true');
      if (coursesResponse.ok) {
        const coursesData = await coursesResponse.json();
        setCourses(coursesData.courses || coursesData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setMessage({ type: 'error', text: 'Failed to load data' });
    }
  };

  // Prepare student options for SearchableSelect
  const studentOptions = students.map(student => ({
    value: student._id,
    label: student.name,
    subtitle: student.email,
    searchText: `${student.name} ${student.email}`
  }));

  // Prepare course options for SearchableSelect
  const courseOptions = courses.map(course => ({
    value: course._id,
    label: course.title,
    searchText: course.title
  }));

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedStudent || !selectedCourse) {
      setMessage({ type: 'error', text: 'Please select both student and course' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      const response = await fetch('/api/certificates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studentId: selectedStudent,
          courseId: selectedCourse,
          issueDate: issueDate,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage({ type: 'success', text: 'Certificate issued successfully!' });
        // Reset form
        setSelectedStudent('');
        setSelectedCourse('');
        setIssueDate(new Date().toISOString().split('T')[0]);
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to issue certificate' });
      }
    } catch (error) {
      console.error('Error issuing certificate:', error);
      setMessage({ type: 'error', text: 'An error occurred while issuing the certificate' });
    } finally {
      setLoading(false);
    }
  };

  if (status === 'loading') {
    return (
      <ModernAdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
        </div>
      </ModernAdminLayout>
    );
  }

  return (
    <ModernAdminLayout>
      <div className="space-y-6">
        {/* Header Section */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
              </svg>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Issue Certificate</h1>
              <p className="text-gray-600">Create and issue certificates to students</p>
            </div>
          </div>
        </div>

        {/* Form Section */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
          {message && (
            <div className={`mb-6 p-4 rounded-lg border ${
              message.type === 'success'
                ? 'bg-green-50 border-green-200 text-green-800'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center space-x-3">
                {message.type === 'success' ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                )}
                <span>{message.text}</span>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Student Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Student
                </label>
                <SearchableSelect
                  options={studentOptions}
                  value={selectedStudent}
                  onChange={setSelectedStudent}
                  placeholder="Search by name or email..."
                  required
                />
              </div>

              {/* Course Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Course
                </label>
                <SearchableSelect
                  options={courseOptions}
                  value={selectedCourse}
                  onChange={setSelectedCourse}
                  placeholder="Search for a course..."
                  required
                />
              </div>
            </div>

            {/* Issue Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Issue Date
              </label>
              <input
                type="date"
                value={issueDate}
                onChange={(e) => setIssueDate(e.target.value)}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
                required
              />
            </div>

            {/* Preview Section */}
            {selectedStudent && selectedCourse && (
              <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Certificate Preview</h3>
                <div className="text-gray-700 space-y-2">
                  <p><span className="text-gray-500">Student:</span> {students && students.find(s => s._id === selectedStudent)?.name}</p>
                  <p><span className="text-gray-500">Course:</span> {courses && courses.find(c => c._id === selectedCourse)?.title}</p>
                  <p><span className="text-gray-500">Issue Date:</span> {new Date(issueDate).toLocaleDateString()}</p>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <div className="flex justify-end space-x-4 pt-6">
              <Link
                href="/admin/dashboard"
                className="px-6 py-3 text-gray-700 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-300 font-medium"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Issuing...</span>
                  </div>
                ) : (
                  'Issue Certificate'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </ModernAdminLayout>
  );
}
