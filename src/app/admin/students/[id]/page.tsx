'use client';

import { useState, useEffect, use } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import ModernAdminLayout from '@/components/admin/ModernAdminLayout';

interface Student {
  _id: string;
  name: string;
  email: string;
  joinDate: string;
  lastLogin?: string;
  isActive: boolean;
  profilePicture?: string;
  courses: {
    _id: string;
    courseTitle: string;
    date: string;
    description: string;
    badgeImage: string;
    downloadCount: number;
    shareCount: number;
  }[];
  createdAt: string;
}

interface StudentProfileProps {
  params: Promise<{
    id: string;
  }>;
}

// Function to generate course initials
const getCourseInitials = (courseTitle: string): string => {
  // Remove common words and symbols
  const cleanTitle = courseTitle.replace(/&/g, '').trim();

  // Split by spaces and get first letter of each word
  const words = cleanTitle.split(/\s+/).filter(word => word.length > 0);

  if (words.length === 1) {
    // Single word: take first two letters
    return words[0].substring(0, 2).toUpperCase();
  } else {
    // Multiple words: take first letter of first two words
    return words.slice(0, 2).map(word => word.charAt(0)).join('').toUpperCase();
  }
};

export default function StudentProfile({ params }: StudentProfileProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [student, setStudent] = useState<Student | null>(null);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [editForm, setEditForm] = useState({
    name: '',
    email: '',
    isActive: true
  });

  // Unwrap params using React.use()
  const resolvedParams = use(params);

  useEffect(() => {
    if (status === 'loading') return;
    if (!session?.user) {
      router.push('/admin/login');
      return;
    }
    fetchStudent();
  }, [session, status, router, resolvedParams.id]);

  const fetchStudent = async () => {
    try {
      const response = await fetch(`/api/students/${resolvedParams.id}`);
      if (response.ok) {
        const data = await response.json();
        setStudent(data.student);
        setEditForm({
          name: data.student.name,
          email: data.student.email,
          isActive: data.student.isActive
        });
      } else if (response.status === 404) {
        router.push('/admin/students');
      }
    } catch (error) {
      console.error('Error fetching student:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStudent = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch(`/api/students/${resolvedParams.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (response.ok) {
        await fetchStudent();
        setEditMode(false);
      }
    } catch (error) {
      console.error('Error updating student:', error);
    }
  };

  const downloadCertificate = async (certificateId: string) => {
    try {
      const response = await fetch(`/api/certificates/download/${certificateId}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `certificate-${certificateId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('Failed to download certificate');
      }
    } catch (error) {
      console.error('Error downloading certificate:', error);
      alert('Failed to download certificate');
    }
  };

  if (status === 'loading' || loading) {
    return (
      <ModernAdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
        </div>
      </ModernAdminLayout>
    );
  }

  if (!student) {
    return (
      <ModernAdminLayout>
        <div className="bg-white rounded-xl p-12 shadow-sm border border-gray-200 text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Student Not Found</h3>
          <p className="text-gray-600 mb-6">The student you're looking for doesn't exist.</p>
          <Link
            href="/admin/students"
            className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-sm"
          >
            Back to Students
          </Link>
        </div>
      </ModernAdminLayout>
    );
  }

  return (
    <ModernAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link
                href="/admin/students"
                className="text-gray-400 hover:text-gray-600 transition-colors duration-300 p-2 hover:bg-gray-100 rounded-lg"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Student Profile</h1>
                  <p className="text-gray-600">Manage student information and track progress</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={() => setEditMode(!editMode)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 shadow-sm ${
                  editMode
                    ? 'bg-gray-600 hover:bg-gray-700 text-white'
                    : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white'
                }`}
              >
                {editMode ? 'Cancel' : 'Edit Student'}
              </button>
            </div>
          </div>
        </div>

        {/* Student Information */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
          <div className="flex items-start gap-8">
            <div className="w-24 h-24 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center text-white font-bold text-3xl shadow-lg">
              {student.name.charAt(0).toUpperCase()}
            </div>

            <div className="flex-1">
              {editMode ? (
                <form onSubmit={handleUpdateStudent} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name
                      </label>
                      <input
                        type="text"
                        required
                        value={editForm.name}
                        onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                        className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        required
                        value={editForm.email}
                        onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                        className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
                      />
                    </div>
                  </div>

                  <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <input
                      type="checkbox"
                      id="isActive"
                      checked={editForm.isActive}
                      onChange={(e) => setEditForm({ ...editForm, isActive: e.target.checked })}
                      className="h-5 w-5 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isActive" className="text-sm text-gray-700 font-medium">
                      Student account is active
                    </label>
                  </div>

                  <div className="flex gap-4">
                    <button
                      type="submit"
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 shadow-sm"
                    >
                      Save Changes
                    </button>
                    <button
                      type="button"
                      onClick={() => setEditMode(false)}
                      className="px-6 py-3 text-gray-700 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-300 font-medium"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-6">
                  <div className="flex items-center gap-4">
                    <h2 className="text-2xl font-bold text-gray-900">{student.name}</h2>
                    <span className={`px-3 py-1 rounded-full text-xs font-semibold ${
                      student.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {student.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                      <dt className="text-sm font-medium text-gray-500 mb-1">Email Address</dt>
                      <dd className="text-lg font-semibold text-gray-900 break-all">{student.email}</dd>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                      <dt className="text-sm font-medium text-gray-500 mb-1">Join Date</dt>
                      <dd className="text-lg font-semibold text-gray-900">{new Date(student.joinDate).toLocaleDateString()}</dd>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                      <dt className="text-sm font-medium text-gray-500 mb-1">Certificates Earned</dt>
                      <dd className="text-2xl font-bold text-green-600">{student.courses.length}</dd>
                    </div>
                  </div>

                  {student.lastLogin && (
                    <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
                      <dt className="text-sm font-medium text-gray-500 mb-1">Last Login</dt>
                      <dd className="text-lg font-semibold text-gray-900">{new Date(student.lastLogin).toLocaleDateString()}</dd>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Certificates Section */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center shadow-sm">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900">Certificates</h3>
                <p className="text-gray-600">{student.courses.length} certificate{student.courses.length !== 1 ? 's' : ''} earned</p>
              </div>
            </div>
          </div>

          {student.courses.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gradient-to-r from-gray-500 to-gray-600 rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">No Certificates Yet</h4>
              <p className="text-gray-600">This student hasn't earned any certificates yet.</p>
            </div>
          ) : (
            <div className="grid gap-6">
              {student.courses.map((course) => (
                <div key={course._id} className="bg-gray-50 rounded-lg p-6 border border-gray-200 hover:border-green-200 hover:shadow-md transition-all duration-300 group">
                  <div className="flex items-start gap-6">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center text-white font-bold text-lg group-hover:scale-105 transition-transform duration-300 shadow-sm">
                      {getCourseInitials(course.courseTitle)}
                    </div>

                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="text-xl font-semibold text-gray-900 mb-1">{course.courseTitle}</h4>
                          <p className="text-gray-600 text-sm">{course.description}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500 mb-1">Issued</div>
                          <div className="text-gray-900 font-medium">{new Date(course.date).toLocaleDateString()}</div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-6 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span>Downloads: {course.downloadCount}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                            </svg>
                            <span>Shares: {course.shareCount}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span>ID: {course._id ? course._id.substring(0, 8) + '...' : 'N/A'}</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          {course._id && (
                            <Link
                              href={`/verify/${course._id}`}
                              target="_blank"
                              className="inline-flex items-center justify-center w-8 h-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200 group"
                              title="Verify Certificate"
                            >
                              <svg className="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                              </svg>
                            </Link>
                          )}

                          {course._id && (
                            <button
                              onClick={() => downloadCertificate(course._id)}
                              className="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-all duration-200 group"
                              title="Download Certificate"
                            >
                              <svg className="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Activity Timeline */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">Activity Timeline</h3>
              <p className="text-gray-600">Track student progress and milestones</p>
            </div>
          </div>

          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-sm">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-1">
                  <span className="text-gray-900 font-medium">Student account created</span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{new Date(student.createdAt).toLocaleDateString()}</span>
                </div>
                <p className="text-gray-600 text-sm">Student joined the platform</p>
              </div>
            </div>

            {student.courses.map((course, index) => (
              <div key={course._id} className="flex items-start gap-4">
                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-sm">
                  {getCourseInitials(course.courseTitle)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-1">
                    <span className="text-gray-900 font-medium">Certificate earned</span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{new Date(course.date).toLocaleDateString()}</span>
                  </div>
                  <p className="text-gray-600 text-sm">Completed "{course.courseTitle}"</p>
                </div>
              </div>
            ))}

            {student.lastLogin && (
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-sm">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-1">
                    <span className="text-gray-900 font-medium">Last login</span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{new Date(student.lastLogin).toLocaleDateString()}</span>
                  </div>
                  <p className="text-gray-600 text-sm">Student accessed their account</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </ModernAdminLayout>
  );
}
