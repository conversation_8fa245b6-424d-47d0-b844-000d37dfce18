import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db/connection';
import { Student, Admin } from '@/lib/db/models';
import { Session } from 'next-auth';
import bcrypt from 'bcryptjs';

// POST - Migrate existing unhashed passwords (Admin only)
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions) as Session | null;

    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    await dbConnect();

    // Function to check if password is already hashed
    const isPasswordHashed = (password: string): boolean => {
      return Boolean(password && (password.startsWith('$2a$') || password.startsWith('$2b$') || password.startsWith('$2y$')));
    };

    let studentsUpdated = 0;
    let adminsUpdated = 0;

    // Hash student passwords
    const students = await Student.find({});
    for (const student of students) {
      if (student.password && !isPasswordHashed(student.password)) {
        const hashedPassword = await bcrypt.hash(student.password, 12);
        await Student.findByIdAndUpdate(student._id, { password: hashedPassword });
        studentsUpdated++;
      }
    }

    // Hash admin passwords
    const admins = await Admin.find({});
    for (const admin of admins) {
      if (admin.password && !isPasswordHashed(admin.password)) {
        const hashedPassword = await bcrypt.hash(admin.password, 12);
        await Admin.findByIdAndUpdate(admin._id, { password: hashedPassword });
        adminsUpdated++;
      }
    }

    return NextResponse.json({
      message: 'Password migration completed successfully',
      studentsUpdated,
      adminsUpdated,
      totalUpdated: studentsUpdated + adminsUpdated
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error during password migration:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to migrate passwords' },
      { status: 500 }
    );
  }
}
