import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db/connection';
import { Student, Admin } from '@/lib/db/models';
import { Session } from 'next-auth';

// PUT - Update user information (Admin only)
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;

    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, email, role, isActive } = body;

    await dbConnect();

    // First, try to find the user in the Student collection
    let user = await Student.findById(id);
    let isStudent = true;

    // If not found in Student, try Admin collection
    if (!user) {
      user = await Admin.findById(id);
      isStudent = false;
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Handle role change
    if (role && role !== (isStudent ? 'student' : 'admin')) {
      // Role is changing, need to move user between collections
      const userData = {
        name: name || user.name,
        email: email || user.email,
        password: user.password,
        isActive: isActive !== undefined ? isActive : user.isActive,
        createdAt: user.createdAt
      };

      if (role === 'admin' && isStudent) {
        // Moving from Student to Admin
        const newAdmin = new Admin({
          ...userData,
          username: userData.email,
          role: 'admin',
          permissions: ['read', 'write', 'delete']
        });
        await newAdmin.save();
        await Student.findByIdAndDelete(id);
        
        return NextResponse.json({
          message: 'User role updated successfully',
          user: {
            _id: newAdmin._id,
            name: newAdmin.name,
            email: newAdmin.email,
            role: 'admin',
            isActive: newAdmin.isActive,
            createdAt: newAdmin.createdAt
          }
        });
      } else if (role === 'student' && !isStudent) {
        // Moving from Admin to Student
        const newStudent = new Student({
          ...userData,
          role: 'student',
          joinDate: userData.createdAt,
          courses: []
        });
        await newStudent.save();
        await Admin.findByIdAndDelete(id);
        
        return NextResponse.json({
          message: 'User role updated successfully',
          user: {
            _id: newStudent._id,
            name: newStudent.name,
            email: newStudent.email,
            role: 'student',
            isActive: newStudent.isActive,
            createdAt: newStudent.createdAt
          }
        });
      }
    }

    // Update user in current collection
    const updateData: any = {};
    if (name) updateData.name = name;
    if (email) updateData.email = email;
    if (isActive !== undefined) updateData.isActive = isActive;

    const updatedUser = isStudent 
      ? await Student.findByIdAndUpdate(id, updateData, { new: true }).select('-password')
      : await Admin.findByIdAndUpdate(id, updateData, { new: true }).select('-password');

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'Failed to update user' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'User updated successfully',
      user: {
        _id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: isStudent ? 'student' : 'admin',
        isActive: updatedUser.isActive,
        createdAt: updatedUser.createdAt
      }
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error updating user:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

// DELETE - Delete user (Admin only)
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;

    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Prevent admin from deleting themselves
    if (session.user.id === id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    await dbConnect();

    // Try to delete from Student collection first
    let deletedUser = await Student.findByIdAndDelete(id);
    
    // If not found in Student, try Admin collection
    if (!deletedUser) {
      deletedUser = await Admin.findByIdAndDelete(id);
    }

    if (!deletedUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'User deleted successfully'
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error deleting user:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
