import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/db/connection';
import { Student, Admin } from '@/lib/db/models';
import { Session } from 'next-auth';
import bcrypt from 'bcryptjs';

// GET - Fetch all users (Admin only)
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    await dbConnect();
    
    // Get all students and admins
    const students = await Student.find({}).select('-password').sort({ createdAt: -1 });
    const admins = await Admin.find({}).select('-password').sort({ createdAt: -1 });
    
    // Combine and format users
    const users = [
      ...students.map(student => ({
        _id: student._id,
        name: student.name,
        email: student.email,
        role: 'student' as const,
        isActive: student.isActive,
        createdAt: student.createdAt,
        lastLogin: student.lastLogin
      })),
      ...admins.map(admin => ({
        _id: admin._id,
        name: admin.name,
        email: admin.email,
        role: 'admin' as const,
        isActive: admin.isActive,
        createdAt: admin.createdAt,
        lastLogin: admin.lastLogin
      }))
    ];
    
    return NextResponse.json({ users });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error fetching users:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST - Create a new user (Admin only)
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions) as Session | null;

    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, email, password, role } = body;

    // Validate required fields
    if (!name || !email || !password || !role) {
      return NextResponse.json(
        { error: 'Name, email, password, and role are required' },
        { status: 400 }
      );
    }

    // Validate role
    if (!['admin', 'student'].includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be admin or student' },
        { status: 400 }
      );
    }

    await dbConnect();

    // Check if user with email already exists in both collections
    const existingStudent = await Student.findOne({ email });
    const existingAdmin = await Admin.findOne({ email });
    
    if (existingStudent || existingAdmin) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);

    let user;
    if (role === 'admin') {
      // Create new admin
      user = new Admin({
        name,
        email,
        password: hashedPassword,
        username: email, // Use email as username for admins
        role: 'admin',
        permissions: ['read', 'write', 'delete'], // Default permissions
        isActive: true
      });
    } else {
      // Create new student
      user = new Student({
        name,
        email,
        password: hashedPassword,
        role: 'student',
        isActive: true,
        joinDate: new Date(),
        courses: []
      });
    }

    await user.save();

    // Remove password from response
    const userResponse = user.toObject();
    delete userResponse.password;

    return NextResponse.json({
      message: 'User created successfully',
      user: {
        _id: userResponse._id,
        name: userResponse.name,
        email: userResponse.email,
        role: role,
        isActive: userResponse.isActive,
        createdAt: userResponse.createdAt
      }
    }, { status: 201 });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error creating user:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}
