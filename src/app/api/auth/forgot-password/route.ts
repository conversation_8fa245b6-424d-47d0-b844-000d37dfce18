import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db/connection';
import { Student, Admin, PasswordResetToken } from '@/lib/db/models';
import { emailService } from '@/lib/email/email-service';
import crypto from 'crypto';

// POST - Request password reset
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { email, userType } = await request.json();

    // Validate input
    if (!email || !userType) {
      return NextResponse.json(
        { error: 'Email and user type are required' },
        { status: 400 }
      );
    }

    if (!['student', 'admin'].includes(userType)) {
      return NextResponse.json(
        { error: 'Invalid user type' },
        { status: 400 }
      );
    }

    await dbConnect();

    // Find user based on type
    let user;
    if (userType === 'admin') {
      user = await Admin.findOne({ email, isActive: true });
    } else {
      user = await Student.findOne({ email, isActive: true });
    }

    if (!user) {
      // Don't reveal if user exists or not for security
      return NextResponse.json(
        { message: 'If an account with that email exists, a password reset link has been sent.' },
        { status: 200 }
      );
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

    // Delete any existing reset tokens for this user
    await PasswordResetToken.deleteMany({
      userId: user._id,
      userType
    });

    // Create new reset token
    const passwordResetToken = new PasswordResetToken({
      userId: user._id,
      userType,
      token: resetToken,
      expiresAt,
      used: false
    });

    await passwordResetToken.save();

    // Generate reset URL
    const resetUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/reset-password?token=${resetToken}&type=${userType}`;

    // Send reset email
    const emailSent = await emailService.sendPasswordResetEmail({
      userName: user.name,
      userEmail: user.email,
      resetUrl,
      userType
    });

    if (emailSent) {
      console.log(`✅ Password reset email sent to ${user.email} (${userType})`);
    } else {
      console.warn(`⚠️  Password reset email not sent to ${user.email} - check email configuration`);
    }

    return NextResponse.json(
      { 
        message: 'If an account with that email exists, a password reset link has been sent.',
        emailSent
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error in forgot password:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
