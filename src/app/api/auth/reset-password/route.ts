import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db/connection';
import { Student, Admin, PasswordResetToken } from '@/lib/db/models';
import bcrypt from 'bcryptjs';

// POST - Reset password with token
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { token, password, userType } = await request.json();

    // Validate input
    if (!token || !password || !userType) {
      return NextResponse.json(
        { error: 'Token, password, and user type are required' },
        { status: 400 }
      );
    }

    if (!['student', 'admin'].includes(userType)) {
      return NextResponse.json(
        { error: 'Invalid user type' },
        { status: 400 }
      );
    }

    if (password.length < 6) {
      return NextResponse.json(
        { error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    await dbConnect();

    // Find and validate reset token
    const resetToken = await PasswordResetToken.findOne({
      token,
      userType,
      used: false,
      expiresAt: { $gt: new Date() }
    });

    if (!resetToken) {
      return NextResponse.json(
        { error: 'Invalid or expired reset token' },
        { status: 400 }
      );
    }

    // Find user
    let user;
    if (userType === 'admin') {
      user = await Admin.findById(resetToken.userId);
    } else {
      user = await Student.findById(resetToken.userId);
    }

    if (!user || !user.isActive) {
      return NextResponse.json(
        { error: 'User not found or inactive' },
        { status: 404 }
      );
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Update user password
    user.password = hashedPassword;
    await user.save();

    // Mark token as used
    resetToken.used = true;
    await resetToken.save();

    console.log(`✅ Password reset successful for ${user.email} (${userType})`);

    return NextResponse.json(
      { message: 'Password reset successful' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error in reset password:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - Validate reset token
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    const userType = searchParams.get('type');

    if (!token || !userType) {
      return NextResponse.json(
        { error: 'Token and user type are required' },
        { status: 400 }
      );
    }

    if (!['student', 'admin'].includes(userType)) {
      return NextResponse.json(
        { error: 'Invalid user type' },
        { status: 400 }
      );
    }

    await dbConnect();

    // Find and validate reset token
    const resetToken = await PasswordResetToken.findOne({
      token,
      userType,
      used: false,
      expiresAt: { $gt: new Date() }
    });

    if (!resetToken) {
      return NextResponse.json(
        { error: 'Invalid or expired reset token' },
        { status: 400 }
      );
    }

    // Find user to get name and email
    let user;
    if (userType === 'admin') {
      user = await Admin.findById(resetToken.userId).select('name email');
    } else {
      user = await Student.findById(resetToken.userId).select('name email');
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        valid: true,
        userType,
        userName: user.name,
        userEmail: user.email
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error validating reset token:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
