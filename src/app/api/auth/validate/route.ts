import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import dbConnect from '@/lib/db/connection';
import { Admin, Student } from '@/lib/db/models';

export async function POST(request: NextRequest) {
  try {
    const { email, password, userType } = await request.json();

    console.log(`🔍 Validation request: ${email} as ${userType}`);

    if (!email || !password || !userType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    await dbConnect();

    let user;
    let otherUser;

    if (userType === 'admin') {
      // Check if user exists as admin
      user = await Admin.findOne({ 
        email: email.toLowerCase(),
        isActive: true 
      });
      
      // Check if user exists as student (to prevent cross-login)
      otherUser = await Student.findOne({ 
        email: email.toLowerCase(),
        isActive: true 
      });
      
      if (!user && otherUser) {
        console.log(`❌ Cross-login attempt: ${email} tried to login as ${userType} but is registered as student`);
        return NextResponse.json(
          { error: 'This email is registered as a student. Please use the student login portal.' },
          { status: 401 }
        );
      }
    } else {
      // Check if user exists as student
      user = await Student.findOne({ 
        email: email.toLowerCase(),
        isActive: true 
      });
      
      // Check if user exists as admin (to prevent cross-login)
      otherUser = await Admin.findOne({ 
        email: email.toLowerCase(),
        isActive: true 
      });
      
      if (!user && otherUser) {
        console.log(`❌ Cross-login attempt: ${email} tried to login as ${userType} but is registered as admin`);
        return NextResponse.json(
          { error: 'This email is registered as an admin. Please use the admin login portal.' },
          { status: 401 }
        );
      }
    }

    if (!user || !user.password) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    const isValid = await bcrypt.compare(password, user.password);

    if (!isValid) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      );
    }

    // If we get here, the credentials are valid
    return NextResponse.json(
      { 
        success: true,
        user: {
          id: user._id.toString(),
          email: user.email,
          name: user.name || user.username,
          role: userType
        }
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Validation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
