import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import dbConnect from '@/lib/db/connection';
import { Certificate } from '@/lib/db/models';
import { authOptions } from '@/lib/auth';
import { HTMLToPDFGenerator } from '@/lib/pdf/html-to-pdf-generator';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const certificateId = params.id;
    const session = await getServerSession(authOptions) as Session | null;
    
    await dbConnect();
    
    // Find certificate by certificateId (not MongoDB _id)
    const certificate = await Certificate.findOne({
      certificateId: certificateId,
      isRevoked: false
    })
    .populate('studentId', 'name email')
    .populate('courseId', 'title description badgeImage');
    
    if (!certificate) {
      return NextResponse.json(
        { error: 'Certificate not found if you believe this is a valid certificate ID, please contact support.' },
        { status: 404 }
      );
    }
    
    // Check authorization - admin or the student who owns the certificate
    if (!session || (
      session.user?.role !== 'admin' && 
      session.user?.id !== certificate.studentId._id.toString()
    )) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Generate PDF certificate
    const generator = new HTMLToPDFGenerator();
    const pdfBuffer = await generator.generateCertificate({
      certificate: certificate,
      student: certificate.studentId,
      course: certificate.courseId
    });
    
    // Increment download count
    await Certificate.findByIdAndUpdate(certificate._id, {
      $inc: { downloadCount: 1 }
    });
    
    // Create filename
    const studentName = certificate.studentId.name.replace(/[^a-zA-Z0-9]/g, '_');
    const courseTitle = certificate.courseId.title.replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${studentName}_${courseTitle}_Certificate.pdf`;
    
    // Return PDF response
    return new NextResponse(pdfBuffer as any, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });
    
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error generating certificate PDF:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to generate certificate PDF' },
      { status: 500 }
    );
  }
}
