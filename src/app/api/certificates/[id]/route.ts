import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import dbConnect from '@/lib/db/connection';
import { Certificate } from '@/lib/db/models';
import { authOptions } from '@/lib/auth';

// GET - Fetch specific certificate (Admin only)
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    await dbConnect();
    
    // Find certificate by ID
    const certificate = await Certificate.findById(id)
      .populate('studentId', 'name email')
      .populate('courseId', 'title description badgeImage');
    
    if (!certificate) {
      return NextResponse.json(
        { error: 'Certificate not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ certificate });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error fetching certificate:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to fetch certificate' },
      { status: 500 }
    );
  }
}

// PUT - Update certificate (Admin only)
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { isRevoked, issueDate } = body;
    
    await dbConnect();
    
    // Find and update certificate
    const certificate = await Certificate.findByIdAndUpdate(
      id,
      { 
        ...(isRevoked !== undefined && { isRevoked }),
        ...(issueDate && { issueDate: new Date(issueDate) })
      },
      { new: true }
    )
    .populate('studentId', 'name email')
    .populate('courseId', 'title description badgeImage');
    
    if (!certificate) {
      return NextResponse.json(
        { error: 'Certificate not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      message: 'Certificate updated successfully',
      certificate
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error updating certificate:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to update certificate' },
      { status: 500 }
    );
  }
}

// DELETE - Delete certificate (Admin only)
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    await dbConnect();
    
    // Find and delete certificate
    const certificate = await Certificate.findByIdAndDelete(id);
    
    if (!certificate) {
      return NextResponse.json(
        { error: 'Certificate not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      message: 'Certificate deleted successfully'
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error deleting certificate:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to delete certificate' },
      { status: 500 }
    );
  }
}
