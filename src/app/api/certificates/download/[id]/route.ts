import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db/connection';
import { Certificate } from '@/lib/db/models';
import { HTMLToPDFGenerator } from '@/lib/pdf/html-to-pdf-generator';
import mongoose from 'mongoose';

// Helper function to check if a string is a valid MongoDB ObjectId
function isValidObjectId(id: string): boolean {
  return mongoose.Types.ObjectId.isValid(id) && id.length === 24;
}

// GET - Download certificate as PDF
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const certificateId = params.id;

    await dbConnect();

    let certificate;

    // Check if the ID is an old MongoDB ObjectId (24-character hex string)
    if (isValidObjectId(certificateId)) {
      // Legacy support: Search by MongoDB _id for old certificates
      certificate = await Certificate.findOne({
        _id: certificateId,
        isRevoked: false
      })
      .populate('studentId', 'name email')
      .populate('courseId', 'title description badgeImage');
    } else if (/^[a-fA-F0-9]{32}$/.test(certificateId)) {
      // Handle 32-character hex certificate IDs (another legacy format)
      certificate = await Certificate.findOne({
        $or: [
          { certificateId: certificateId },
          { _id: certificateId } // In case it's stored as _id
        ],
        isRevoked: false
      })
      .populate('studentId', 'name email')
      .populate('courseId', 'title description badgeImage');
    } else {
      // New system: Search by certificateId (UUID or other formats)
      certificate = await Certificate.findOne({
        certificateId: certificateId,
        isRevoked: false
      })
      .populate('studentId', 'name email')
      .populate('courseId', 'title description badgeImage');
    }

    if (!certificate) {
      return NextResponse.json(
        { error: 'Certificate not found if you believe this is a valid certificate ID, please contact support.' },
        { status: 404 }
      );
    }

    // Generate PDF certificate using the same generator as admin
    const generator = new HTMLToPDFGenerator();
    const pdfBuffer = await generator.generateCertificate({
      certificate: certificate,
      student: certificate.studentId,
      course: certificate.courseId
    });

    // Update download count - use appropriate query based on certificate type
    if (isValidObjectId(certificateId)) {
      // Legacy certificate: update by _id
      await Certificate.updateOne(
        { _id: certificateId },
        { $inc: { downloadCount: 1 } }
      );
    } else if (/^[a-fA-F0-9]{32}$/.test(certificateId)) {
      // 32-character hex format: try both certificateId and _id
      await Certificate.updateOne(
        { $or: [{ certificateId: certificateId }, { _id: certificateId }] },
        { $inc: { downloadCount: 1 } }
      );
    } else {
      // New certificate: update by certificateId
      await Certificate.updateOne(
        { certificateId: certificateId },
        { $inc: { downloadCount: 1 } }
      );
    }

    // Create filename
    const studentName = certificate.studentId.name.replace(/[^a-zA-Z0-9]/g, '_');
    const courseTitle = certificate.courseId.title.replace(/[^a-zA-Z0-9]/g, '_');
    const filename = `${studentName}_${courseTitle}_Certificate.pdf`;

    // Return PDF
    return new NextResponse(pdfBuffer as any, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error generating certificate PDF:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to generate certificate PDF', details: errorMessage },
      { status: 500 }
    );
  }
}
