import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import dbConnect from '@/lib/db/connection';
import { Certificate, Student, Course } from '@/lib/db/models';
import { authOptions } from '@/lib/auth';
import { v4 as uuidv4 } from 'uuid';
import { CertificateGenerator } from '@/lib/pdf/certificate-generator';
import { emailService } from '@/lib/email/email-service';

// GET - Fetch all certificates (Admin only)
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    await dbConnect();
    
    // Get all certificates with populated data
    const certificates = await Certificate.find({})
      .populate('studentId', 'name email')
      .populate('courseId', 'title description badgeImage')
      .sort({ createdAt: -1 });
    
    return NextResponse.json({ certificates });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error fetching certificates:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to fetch certificates' },
      { status: 500 }
    );
  }
}

// POST - Issue a new certificate (Admin only)
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { studentId, courseId, issueDate } = body;
    
    // Validate required fields
    if (!studentId || !courseId) {
      return NextResponse.json(
        { error: 'Student ID and Course ID are required' },
        { status: 400 }
      );
    }
    
    await dbConnect();
    
    // Check if student exists
    const student = await Student.findById(studentId);
    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }
    
    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }
    
    // Check if certificate already exists for this student and course
    const existingCertificate = await Certificate.findOne({
      studentId,
      courseId,
      isRevoked: false
    });
    
    if (existingCertificate) {
      return NextResponse.json(
        { error: 'Certificate already exists for this student and course' },
        { status: 409 }
      );
    }
    
    // Generate unique certificate ID
    const certificateId = uuidv4();
    
    // Create new certificate
    const certificate = new Certificate({
      studentId,
      courseId,
      issueDate: issueDate || new Date(),
      certificateId,
      downloadCount: 0,
      shareCount: 0,
      isRevoked: false
    });
    
    await certificate.save();
    
    // Add course to student's courses array if not already present
    if (!student.courses) {
      student.courses = [];
    }
    if (!student.courses.includes(courseId)) {
      student.courses.push(courseId);
      await student.save();
    }
    
    // Populate the certificate data for response
    await certificate.populate('studentId', 'name email');
    await certificate.populate('courseId', 'title description badgeImage');

    // Generate and send certificate email
    let emailSent = false;
    try {
      const certificateGenerator = new CertificateGenerator();
      const certificatePdfBuffer = await certificateGenerator.generateCertificate({
        certificate,
        student,
        course
      });

      // Format issue date
      const formattedIssueDate = new Date(certificate.issueDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      // Send email with certificate attachment
      emailSent = await emailService.sendCertificateEmail(
        {
          studentName: student.name,
          studentEmail: student.email,
          courseName: course.title,
          certificateId: certificate.certificateId,
          issueDate: formattedIssueDate,
          verificationUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/verify/${certificate.certificateId}`
        },
        certificatePdfBuffer
      );

      if (emailSent) {
        console.log(`✅ Certificate email sent successfully to ${student.email}`);
      } else {
        console.warn(`⚠️  Certificate email not sent to ${student.email} - check email configuration`);
      }
    } catch (emailError) {
      console.error('Error sending certificate email:', emailError);
      emailSent = false;
      // Don't fail the certificate creation if email fails
    }

    return NextResponse.json({
      message: 'Certificate issued successfully',
      certificate,
      emailSent,
      emailNote: emailSent ? 'Certificate email sent to student' : 'Certificate created but email not sent - check email configuration'
    }, { status: 201 });
    
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error issuing certificate:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to issue certificate' },
      { status: 500 }
    );
  }
}
