import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import dbConnect from '@/lib/db/connection';
import { Course } from '@/lib/db/models';
import { authOptions } from '@/lib/auth';

// GET - Fetch specific course
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    
    await dbConnect();
    
    // Find course by ID
    const course = await Course.findById(id);
    
    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    // Map database fields to frontend expected fields
    const mappedCourse = {
      _id: course._id,
      title: course.title,
      description: course.description,
      duration: course.duration,
      badgeImage: course.badgeImage,
      certificateTemplate: course.certificateTemplate,
      isActive: course.active, // Map 'active' to 'isActive'
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    };

    return NextResponse.json({ course: mappedCourse });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error fetching course:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to fetch course' },
      { status: 500 }
    );
  }
}

// PUT - Update course (Admin only)
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { title, description, duration, isActive } = body;
    
    await dbConnect();
    
    // Check if another course with same title exists (excluding current course)
    if (title) {
      const existingCourse = await Course.findOne({ 
        title, 
        _id: { $ne: id } 
      });
      if (existingCourse) {
        return NextResponse.json(
          { error: 'Course with this title already exists' },
          { status: 409 }
        );
      }
    }
    
    // Find and update course
    const course = await Course.findByIdAndUpdate(
      id,
      {
        ...(title && { title }),
        ...(description && { description }),
        ...(duration && { duration }),
        ...(isActive !== undefined && { active: isActive })
      },
      { new: true }
    );
    
    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    // Map database fields to frontend expected fields
    const mappedCourse = {
      _id: course._id,
      title: course.title,
      description: course.description,
      duration: course.duration,
      badgeImage: course.badgeImage,
      certificateTemplate: course.certificateTemplate,
      isActive: course.active, // Map 'active' to 'isActive'
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    };

    return NextResponse.json({
      message: 'Course updated successfully',
      course: mappedCourse
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error updating course:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to update course' },
      { status: 500 }
    );
  }
}

// DELETE - Delete course (Admin only)
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    await dbConnect();
    
    // Find and delete course
    const course = await Course.findByIdAndDelete(id);
    
    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      message: 'Course deleted successfully'
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error deleting course:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to delete course' },
      { status: 500 }
    );
  }
}
