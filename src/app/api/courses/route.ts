import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import dbConnect from '@/lib/db/connection';
import { Course } from '@/lib/db/models';
import { authOptions } from '@/lib/auth';

// GET - Fetch all courses
export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    await dbConnect();
    
    // Get URL search params
    const { searchParams } = new URL(request.url);
    const activeOnly = searchParams.get('active') === 'true';
    
    // Build query
    const query = activeOnly ? { active: true } : {};
    
    // Get all courses
    const courses = await Course.find(query).sort({ createdAt: -1 });

    // Map database fields to frontend expected fields
    const mappedCourses = courses.map(course => ({
      _id: course._id,
      title: course.title,
      description: course.description,
      duration: course.duration,
      badgeImage: course.badgeImage,
      certificateTemplate: course.certificateTemplate,
      isActive: course.active, // Map 'active' to 'isActive'
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    }));

    return NextResponse.json(mappedCourses);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error fetching courses:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to fetch courses' },
      { status: 500 }
    );
  }
}

// POST - Create a new course (Admin only)
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await request.json();
    const { title, description, duration, isActive } = body;

    // Validate required fields
    if (!title || !description || !duration) {
      return NextResponse.json(
        { error: 'Title, description, and duration are required' },
        { status: 400 }
      );
    }
    
    await dbConnect();
    
    // Check if course with same title already exists
    const existingCourse = await Course.findOne({ title });
    if (existingCourse) {
      return NextResponse.json(
        { error: 'Course with this title already exists' },
        { status: 409 }
      );
    }
    
    // Create new course
    const course = new Course({
      title,
      description,
      duration,
      badgeImage: '/img/fullstackbadge.svg',
      certificateTemplate: 'default',
      active: isActive !== undefined ? isActive : true
    });
    
    await course.save();

    // Map database fields to frontend expected fields
    const mappedCourse = {
      _id: course._id,
      title: course.title,
      description: course.description,
      duration: course.duration,
      badgeImage: course.badgeImage,
      certificateTemplate: course.certificateTemplate,
      isActive: course.active, // Map 'active' to 'isActive'
      createdAt: course.createdAt,
      updatedAt: course.updatedAt
    };

    return NextResponse.json({
      message: 'Course created successfully',
      course: mappedCourse
    }, { status: 201 });
    
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error creating course:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to create course' },
      { status: 500 }
    );
  }
}
