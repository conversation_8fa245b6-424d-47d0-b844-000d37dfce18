import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import dbConnect from '@/lib/db/connection';
import { Student, Certificate, Course } from '@/lib/db/models';
import { authOptions } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;
    
    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Check if user is admin or the student themselves
    if (session.user?.role !== 'admin' && session.user?.id !== id) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      );
    }
    
    await dbConnect();

    // Find student by ID
    const student = await Student.findById(id);

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    // Get student's certificates with course details
    const certificates = await Certificate.find({
      studentId: id,
      isRevoked: false
    })
    .populate('courseId', 'title description badgeImage')
    .sort({ issueDate: -1 });

    // Format the response to match the old structure for compatibility
    const studentWithCertificates = {
      ...student.toObject(),
      courses: certificates.map(cert => ({
        _id: cert.certificateId || cert._id.toString(), // Use certificateId for new certs, _id for legacy
        courseTitle: cert.courseId.title,
        date: cert.issueDate,
        description: cert.courseId.description,
        badgeImage: cert.courseId.badgeImage,
        downloadCount: cert.downloadCount,
        shareCount: cert.shareCount
      }))
    };

    return NextResponse.json({ student: studentWithCertificates });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error fetching student:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to fetch student' },
      { status: 500 }
    );
  }
}

// PUT - Update student information (Admin only)
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const params = await context.params;
    const id = params.id;
    const session = await getServerSession(authOptions) as Session | null;

    // Check if user is authenticated and is an admin
    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, email, isActive } = body;

    await dbConnect();

    // Find and update student
    const student = await Student.findByIdAndUpdate(
      id,
      {
        ...(name && { name }),
        ...(email && { email }),
        ...(isActive !== undefined && { isActive })
      },
      { new: true }
    );

    if (!student) {
      return NextResponse.json(
        { error: 'Student not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Student updated successfully',
      student
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error updating student:', errorMessage);
    return NextResponse.json(
      { error: 'Failed to update student' },
      { status: 500 }
    );
  }
}