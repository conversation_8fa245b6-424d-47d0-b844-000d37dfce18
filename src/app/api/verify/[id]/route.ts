import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/db/connection';
import { Certificate, Student, Course } from '@/lib/db/models';
import mongoose from 'mongoose';

// Helper function to check if a string is a valid MongoDB ObjectId
function isValidObjectId(id: string): boolean {
  return mongoose.Types.ObjectId.isValid(id) && id.length === 24;
}

// Helper function to check if a string looks like a certificate ID (hex string, 24 or 32 chars)
function isValidCertificateId(id: string): boolean {
  // Check for MongoDB ObjectId (24 chars) or other hex formats (32 chars)
  return /^[a-fA-F0-9]{24}$/.test(id) || /^[a-fA-F0-9]{32}$/.test(id) ||
         // Also check for UUID format
         /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
}

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const resolvedParams = await params;
    const certificateId = resolvedParams.id;

    await dbConnect();

    let certificate;

    // Check if the ID is an old MongoDB ObjectId (24-character hex string)
    if (isValidObjectId(certificateId)) {
      // Legacy support: Search by MongoDB _id for old certificates
      certificate = await Certificate.findOne({
        _id: certificateId,
        isRevoked: false
      })
      .populate('studentId', 'name email')
      .populate('courseId', 'title description badgeImage');
    } else if (/^[a-fA-F0-9]{32}$/.test(certificateId)) {
      // Handle 32-character hex certificate IDs (another legacy format)
      certificate = await Certificate.findOne({
        $or: [
          { certificateId: certificateId },
          { _id: certificateId } // In case it's stored as _id
        ],
        isRevoked: false
      })
      .populate('studentId', 'name email')
      .populate('courseId', 'title description badgeImage');
    } else {
      // New system: Search by certificateId (UUID or other formats)
      certificate = await Certificate.findOne({
        certificateId: certificateId,
        isRevoked: false
      })
      .populate('studentId', 'name email')
      .populate('courseId', 'title description badgeImage');
    }

    if (!certificate) {
      return NextResponse.json(
        { error: 'Certificate not found if you believe this is a valid certificate ID, please contact support.' },
        { status: 404 }
      );
    }

    // Return certificate details
    return NextResponse.json({
      verified: true,
      certificate: {
        // For legacy certificates, use the MongoDB _id if certificateId doesn't exist
        id: certificate.certificateId || certificate._id.toString(),
        issueDate: certificate.issueDate,
        downloadCount: certificate.downloadCount || 0,
        shareCount: certificate.shareCount || 0
      },
      student: {
        name: certificate.studentId.name,
        email: certificate.studentId.email
      },
      course: {
        title: certificate.courseId.title,
        description: certificate.courseId.description,
        badgeImage: certificate.courseId.badgeImage
      },
      // Add a flag to indicate if this is a legacy certificate
      isLegacy: isValidObjectId(certificateId) || /^[a-fA-F0-9]{32}$/.test(certificateId)
    });
  } catch (error) {
    console.error('Error verifying certificate:', error);
    return NextResponse.json(
      { error: 'Failed to verify certificate' },
      { status: 500 }
    );
  }
}