'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';

interface Course {
  _id: string;
  title: string;
  description: string;
  duration: string;
  badgeImage: string;
  isActive: boolean;
  createdAt: string;
}

export default function CoursesPage() {
  const { data: session } = useSession();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const response = await fetch('/api/courses');
        if (response.ok) {
          const data = await response.json();
          console.log('Courses API response:', data);
          // The API returns courses directly as an array
          setCourses(Array.isArray(data) ? data : []);
        } else {
          console.error('Failed to fetch courses:', response.status, response.statusText);
          setError('Failed to load courses');
        }
      } catch (err) {
        setError('Network error. Please try again.');
        console.error('Error fetching courses:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, []);

  // Helper function to get course initials for display
  const getCourseInitials = (courseTitle: string): string => {
    const words = courseTitle.split(' ').filter(word => word.length > 0 && word !== '&');

    if (words.length === 1) {
      // Single word: take first 2 characters
      return words[0].substring(0, 2).toUpperCase();
    } else {
      // Multiple words: take first character of first 2 words
      return words.slice(0, 2).map(word => word.charAt(0)).join('').toUpperCase();
    }
  };

  // Helper function to get course colors based on index
  const getCourseColors = (index: number) => {
    const colorSchemes = [
      {
        gradient: 'from-orange-500 to-red-600',
        border: 'hover:border-orange-400/50',
        badge: 'bg-orange-500/20 text-orange-300',
        link: 'text-orange-400 hover:text-orange-300'
      },
      {
        gradient: 'from-yellow-500 to-orange-600',
        border: 'hover:border-yellow-400/50',
        badge: 'bg-yellow-500/20 text-yellow-300',
        link: 'text-yellow-400 hover:text-yellow-300'
      },
      {
        gradient: 'from-cyan-500 to-teal-600',
        border: 'hover:border-cyan-400/50',
        badge: 'bg-cyan-500/20 text-cyan-300',
        link: 'text-cyan-400 hover:text-cyan-300'
      },
      {
        gradient: 'from-green-500 to-emerald-600',
        border: 'hover:border-green-400/50',
        badge: 'bg-green-500/20 text-green-300',
        link: 'text-green-400 hover:text-green-300'
      },
      {
        gradient: 'from-purple-500 to-indigo-600',
        border: 'hover:border-purple-400/50',
        badge: 'bg-purple-500/20 text-purple-300',
        link: 'text-purple-400 hover:text-purple-300'
      },
      {
        gradient: 'from-pink-500 to-rose-600',
        border: 'hover:border-pink-400/50',
        badge: 'bg-pink-500/20 text-pink-300',
        link: 'text-pink-400 hover:text-pink-300'
      }
    ];

    return colorSchemes[index % colorSchemes.length];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900">
      {/* Navigation */}
      <nav className="bg-black/20 backdrop-blur-sm border-b border-white/10 fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl overflow-hidden">
                <Image
                  src="/capalogo.png"
                  alt="CapacityBay Logo"
                  width={40}
                  height={40}
                  className="w-full h-full object-cover"
                />
              </div>
              <span className="text-white font-bold text-xl">CapacityBay</span>
            </Link>
            
            <div className="flex items-center space-x-4">
              {session ? (
                <Link
                  href={session.user?.role === 'admin' ? '/admin/dashboard' : '/student/dashboard'}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-2 rounded-xl hover:from-green-600 hover:to-emerald-700 transition duration-300 font-medium"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  href="/login"
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-2 rounded-xl hover:from-green-600 hover:to-emerald-700 transition duration-300 font-medium"
                >
                  Student Portal
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <div className="relative pt-28 pb-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Our <span className="bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent">Courses</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Explore our comprehensive collection of professional development courses designed to advance your career and skills.
          </p>
        </div>
      </div>

      {/* Courses Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading courses...</p>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="text-red-400 text-6xl mb-4">⚠️</div>
            <h3 className="text-xl font-semibold text-white mb-4">Error Loading Courses</h3>
            <p className="text-gray-300 mb-6">{error}</p>
            <button 
              onClick={() => window.location.reload()} 
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-300"
            >
              Try Again
            </button>
          </div>
        ) : courses.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📚</div>
            <h3 className="text-xl font-semibold text-white mb-4">No Courses Available</h3>
            <p className="text-gray-300 mb-6">
              We&apos;re working on adding new courses. Check back soon!
            </p>
            <Link 
              href="/" 
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-300"
            >
              Return to Homepage
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {courses.map((course, index) => {
              const colors = getCourseColors(index);
              const courseInitials = getCourseInitials(course.title);

              return (
                <div key={course._id} className={`glass-strong rounded-2xl overflow-hidden border border-white/20 ${colors.border} transition duration-500 group`}>
                  <div className={`h-48 bg-gradient-to-br ${colors.gradient} flex items-center justify-center p-6 relative overflow-hidden`}>
                    <div className="absolute inset-0 bg-black/20"></div>
                    <div className={`w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 transition duration-500 relative z-10 shadow-lg`}>
                      <span className="text-white font-bold text-2xl">
                        {courseInitials}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="font-bold text-xl mb-3 text-white">{course.title}</h3>
                    <p className="text-gray-300 text-sm mb-4 leading-relaxed line-clamp-2">{course.description}</p>
                    <div className="flex items-center justify-center mb-4">
                      <span className={`text-xs ${colors.badge} px-3 py-1 rounded-full`}>
                        {course.duration}
                      </span>
                    </div>

                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-black/20 backdrop-blur-sm border-t border-white/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex justify-center items-center space-x-3 mb-4">
              <div className="w-10 h-10 rounded-xl overflow-hidden">
                <Image
                  src="/capalogo.png"
                  alt="CapacityBay Logo"
                  width={40}
                  height={40}
                  className="w-full h-full object-cover"
                />
              </div>
              <span className="text-white font-bold text-xl">CapacityBay</span>
            </div>
            <p className="text-gray-400 mb-6">
              Empowering the next generation of developers with industry-recognized certifications.
            </p>
            <div className="flex justify-center space-x-6">
              <Link href="/" className="text-gray-400 hover:text-white transition-colors">Home</Link>
              <Link href="/verify" className="text-gray-400 hover:text-white transition-colors">Verify</Link>
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">Privacy</Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">Terms</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
