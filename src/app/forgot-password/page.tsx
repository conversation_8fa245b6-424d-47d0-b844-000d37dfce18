'use client';

import { useState, FormEvent, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import LoginLayout from '@/components/auth/LoginLayout';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import Alert from '@/components/ui/Alert';

function ForgotPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const userType = searchParams.get('type') || 'student';
  
  const [email, setEmail] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          userType
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage(data.message);
        setEmail(''); // Clear form
      } else {
        setError(data.error || 'An error occurred');
      }
    } catch (err) {
      setError('Network error. Please try again.');
      console.error('Forgot password error:', err);
    } finally {
      setLoading(false);
    }
  };

  const isAdmin = userType === 'admin';

  return (
    <LoginLayout 
      title="Forgot Password" 
      subtitle={`Reset your ${isAdmin ? 'admin' : 'student'} account password`}
    >
      <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
        {error && <Alert type="error" message={error} />}
        {message && <Alert type="success" message={message} />}
        
        <div className="space-y-4">
          <div className="text-center mb-6">
            <p className="text-gray-300 text-sm">
              Enter your email address and we'll send you a link to reset your password.
            </p>
          </div>

          <Input
            label="Email address"
            name="email"
            type="email"
            autoComplete="email"
            required
            placeholder="Enter your email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />

          <div className="text-xs text-gray-400 bg-slate-800/50 p-3 rounded-lg">
            <div className="flex items-center mb-2">
              <span className="text-yellow-400 mr-2">⚠️</span>
              <span className="font-medium">Account Type: {isAdmin ? 'Administrator' : 'Student'}</span>
            </div>
            <p>
              Make sure this is the correct account type. If you need to reset a different account type, 
              use the links below.
            </p>
          </div>
        </div>

        <Button type="submit" isLoading={loading}>
          {loading ? 'Sending Reset Link...' : 'Send Reset Link'}
        </Button>
        
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center space-x-4 text-sm">
            <Link 
              href={`/login${isAdmin ? '' : ''}`} 
              className="font-medium text-green-400 hover:text-green-300 transition-colors duration-300"
            >
              Back to {isAdmin ? 'Student' : 'Student'} Login
            </Link>
            <span className="text-gray-500">|</span>
            <Link 
              href={`/forgot-password?type=${isAdmin ? 'student' : 'admin'}`}
              className="font-medium text-blue-400 hover:text-blue-300 transition-colors duration-300"
            >
              {isAdmin ? 'Student' : 'Admin'} Reset
            </Link>
          </div>
          
          <Link 
            href="/" 
            className="block font-medium text-gray-400 hover:text-gray-300 transition-colors duration-300"
          >
            Return to Homepage
          </Link>
        </div>
      </form>
    </LoginLayout>
  );
}

export default function ForgotPasswordPage() {
  return (
    <Suspense fallback={
      <LoginLayout title="Forgot Password" subtitle="Loading...">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
        </div>
      </LoginLayout>
    }>
      <ForgotPasswordForm />
    </Suspense>
  );
}
