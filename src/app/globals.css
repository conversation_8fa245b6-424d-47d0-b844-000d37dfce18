@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  /* Base Colors */
  --background: #ffffff;
  --foreground: #171717;

  /* Green Brand Color Palette */
  --primary-50: #f0fdf4;
  --primary-100: #dcfce7;
  --primary-200: #bbf7d0;
  --primary-300: #86efac;
  --primary-400: #4ade80;
  --primary-500: #22c55e;
  --primary-600: #16a34a;
  --primary-700: #15803d;
  --primary-800: #166534;
  --primary-900: #14532d;

  /* Gradient Colors - Updated to Green Theme */
  --gradient-primary: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  --gradient-secondary: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --gradient-success: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  --gradient-ai: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #10b981 100%);

  /* Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(34, 197, 94, 0.2);

  /* Surfaces */
  --surface-primary: #ffffff;
  --surface-secondary: #f8fafc;
  --surface-elevated: #ffffff;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --surface-primary: #1e293b;
    --surface-secondary: #334155;
    --surface-elevated: #475569;
    --glass-bg: rgba(15, 23, 42, 0.8);
    --glass-border: rgba(148, 163, 184, 0.2);
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-ai);
}

/* Glass Morphism Utility Classes */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

.glass-strong {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px 0 rgba(34, 197, 94, 0.3);
}

/* Gradient Text */
.gradient-text {
  background: var(--gradient-ai);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Floating Animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Glow Effects - Updated to Green Theme */
.glow {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.glow-hover:hover {
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.5);
  transition: box-shadow 0.3s ease;
}
