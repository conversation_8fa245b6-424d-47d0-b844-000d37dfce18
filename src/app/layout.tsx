import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Providers from "./providers";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CapacityBay - Certificate Verification System",
  description: "Verify authentic certificates issued by CapacityBay. Secure, blockchain-powered certificate verification with instant validation.",
  keywords: "certificate verification, CapacityBay, digital certificates, blockchain verification, authentic certificates",
  authors: [{ name: "CapacityBay" }],
  creator: "CapacityBay",
  publisher: "CapacityBay",
  openGraph: {
    title: "CapacityBay - Certificate Verification System",
    description: "Verify authentic certificates issued by CapacityBay. Secure, blockchain-powered certificate verification.",
    type: "website",
    locale: "en_US",
    siteName: "CapacityBay Certificate Verification",
  },
  twitter: {
    card: "summary_large_image",
    title: "CapacityBay - Certificate Verification System",
    description: "Verify authentic certificates issued by CapacityBay. Secure, blockchain-powered certificate verification.",
  },
  robots: {
    index: true,
    follow: true,
  },
  icons: {
    icon: [
      { url: "/capalogo.png", sizes: "32x32", type: "image/png" },
      { url: "/capalogo.png", sizes: "16x16", type: "image/png" }
    ],
    shortcut: "/capalogo.png",
    apple: "/capalogo.png",
    other: [
      {
        rel: "icon",
        type: "image/png",
        sizes: "192x192",
        url: "/capalogo.png",
      },
    ],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
