'use client';

import Image from "next/image";
import Link from "next/link";
import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MobileMenu from "@/components/MobileMenu";

interface Course {
  _id: string;
  title: string;
  description: string;
  duration: string;
  badgeImage: string;
  isActive: boolean;
  createdAt: string;
}

export default function Home() {
  const { data: session } = useSession();
  const router = useRouter();
  const [courses, setCourses] = useState<Course[]>([]);
  const [coursesLoading, setCoursesLoading] = useState<boolean>(true);
  const [quickVerifyId, setQuickVerifyId] = useState<string>('');
  const [quickVerifyLoading, setQuickVerifyLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const response = await fetch('/api/courses?active=true');
        if (response.ok) {
          const data = await response.json();
          // Limit to 4 courses for homepage
          setCourses(Array.isArray(data) ? data.slice(0, 4) : []);
        }
      } catch (err) {
        console.error('Error fetching courses:', err);
      } finally {
        setCoursesLoading(false);
      }
    };

    fetchCourses();
  }, []);

  // Handle quick verification form submission
  const handleQuickVerify = (e: React.FormEvent) => {
    e.preventDefault();
    if (quickVerifyId.trim()) {
      setQuickVerifyLoading(true);
      router.push(`/verify/${quickVerifyId.trim()}`);
    }
  };

  // Helper function to get course initials for display
  const getCourseInitials = (courseTitle: string): string => {
    const words = courseTitle.split(' ').filter(word => word.length > 0 && word !== '&');

    if (words.length === 1) {
      // Single word: take first 2 characters
      return words[0].substring(0, 2).toUpperCase();
    } else {
      // Multiple words: take first character of first 2 words
      return words.slice(0, 2).map(word => word.charAt(0)).join('').toUpperCase();
    }
  };

  // Helper function to get course colors based on index
  const getCourseColors = (index: number) => {
    const colorSchemes = [
      {
        gradient: 'from-orange-500 to-red-600',
        border: 'hover:border-orange-400/50',
        badge: 'bg-orange-500/20 text-orange-300',
        link: 'text-orange-400 hover:text-orange-300'
      },
      {
        gradient: 'from-yellow-500 to-orange-600',
        border: 'hover:border-yellow-400/50',
        badge: 'bg-yellow-500/20 text-yellow-300',
        link: 'text-yellow-400 hover:text-yellow-300'
      },
      {
        gradient: 'from-cyan-500 to-teal-600',
        border: 'hover:border-cyan-400/50',
        badge: 'bg-cyan-500/20 text-cyan-300',
        link: 'text-cyan-400 hover:text-cyan-300'
      },
      {
        gradient: 'from-green-500 to-emerald-600',
        border: 'hover:border-green-400/50',
        badge: 'bg-green-500/20 text-green-300',
        link: 'text-green-400 hover:text-green-300'
      }
    ];

    return colorSchemes[index % colorSchemes.length];
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-teal-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-500"></div>
      </div>

      {/* Navigation */}
      <nav className="glass border-b border-white/10 fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl overflow-hidden glow">
                <Image
                  src="/capalogo.png"
                  alt="CapacityBay Logo"
                  width={40}
                  height={40}
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <span className="text-xl font-bold gradient-text">CapacityBay</span>
                <div className="text-xs text-gray-400">Certificate Verification</div>
              </div>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link href="#about" className="text-gray-300 hover:text-white transition duration-300 hover:glow">About</Link>
              <Link href="#features" className="text-gray-300 hover:text-white transition duration-300 hover:glow">Features</Link>
              <Link href="#courses" className="text-gray-300 hover:text-white transition duration-300 hover:glow">Courses</Link>
              <Link href="#verify" className="text-gray-300 hover:text-white transition duration-300 hover:glow">Verify</Link>
            </div>
            <div className="hidden md:flex items-center space-x-4">
              {session ? (
                <Link
                  href={session.user?.role === 'admin' ? '/admin/dashboard' : '/student/dashboard'}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-2 rounded-xl hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover font-medium"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  href="/login"
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-2 rounded-xl hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover font-medium"
                >
                  Student Portal
                </Link>
              )}
            </div>
            <MobileMenu />
          </div>
        </div>
      </nav>

      {/* Hero Section - AI Agent Inspired */}
      <section className="relative pt-40 pb-32 overflow-hidden">
        {/* Floating geometric shapes */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-32 h-32 bg-green-500/20 rounded-full blur-xl float-animation"></div>
          <div className="absolute top-40 right-32 w-24 h-24 bg-emerald-500/20 rounded-lg rotate-45 blur-lg float-animation delay-1000"></div>
          <div className="absolute bottom-32 left-1/3 w-40 h-40 bg-teal-500/10 rounded-full blur-2xl float-animation delay-500"></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-white/10 rounded-full border border-white/20 mb-8">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
              <span className="text-sm text-gray-300">Next-Generation Certificate Verification</span>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-8 leading-tight">
              <span className="gradient-text">Secure</span> & <span className="gradient-text">Verifiable</span><br />
              <span className="text-white">Digital Certificates</span>
            </h1>

            <p className="text-xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
              Experience the future of credential verification with CapacityBay&apos;s advanced platform.
              Instant verification, tamper-proof security, and global recognition.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
              {session ? (
                <Link
                  href={session.user?.role === 'admin' ? '/admin/dashboard' : '/student/dashboard'}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover transform hover:scale-105"
                >
                  Go to Dashboard
                </Link>
              ) : (
                <Link
                  href="/login"
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover transform hover:scale-105"
                >
                  Access Student Portal
                </Link>
              )}
              <Link
                href="#verify"
                className="glass text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/20 transition duration-300 border border-white/20"
              >
                Verify Certificate
              </Link>
            </div>
          </div>

          {/* Certificate Verification Widget */}
          <div className="max-w-2xl mx-auto">
            <div className="glass-strong rounded-2xl p-8 border border-white/20">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-white mb-2">Quick Verification</h3>
                <p className="text-gray-300">Enter a certificate ID to verify its authenticity</p>
              </div>

              <form onSubmit={handleQuickVerify} className="flex flex-col sm:flex-row gap-4">
                <input
                  type="text"
                  value={quickVerifyId}
                  onChange={(e) => setQuickVerifyId(e.target.value)}
                  placeholder="Enter certificate ID (e.g., 631f854a9b27e97f9ebce5b3)"
                  className="flex-grow px-6 py-4 bg-white/10 border border-white/20 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-400 text-white placeholder-gray-400 backdrop-blur-sm"
                  required
                />
                <button
                  type="submit"
                  disabled={quickVerifyLoading || !quickVerifyId.trim()}
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {quickVerifyLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Verifying...</span>
                    </div>
                  ) : (
                    'Verify Now'
                  )}
                </button>
              </form>

              <div className="mt-6 text-center">
                <p className="text-sm text-gray-400">
                  Or scan the QR code on your certificate for instant verification
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section - AI Agent Style */}
      <section id="about" className="py-24 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">Revolutionizing</span> <span className="text-white">Digital Credentials</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              CapacityBay&apos;s certification platform combines cutting-edge technology
              with uncompromising security to deliver the future of credential verification.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <div className="glass-strong rounded-2xl p-8 border border-white/20 hover:border-green-400/50 transition duration-500 group">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 glow group-hover:scale-110 transition duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Quantum-Safe Security</h3>
              <p className="text-gray-300 leading-relaxed">Advanced cryptographic protection with unique identifiers and tamper-proof verification systems.</p>
            </div>

            <div className="glass-strong rounded-2xl p-8 border border-white/20 hover:border-emerald-400/50 transition duration-500 group">
              <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 glow group-hover:scale-110 transition duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Global Recognition</h3>
              <p className="text-gray-300 leading-relaxed">Internationally accepted credentials recognized by leading employers and institutions worldwide.</p>
            </div>

            <div className="glass-strong rounded-2xl p-8 border border-white/20 hover:border-green-400/50 transition duration-500 group">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6 glow group-hover:scale-110 transition duration-300">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Instant Verification</h3>
              <p className="text-gray-300 leading-relaxed">Lightning-fast verification powered by advanced algorithms and distributed verification networks.</p>
            </div>
          </div>

          <div className="text-center">
            <div className="glass rounded-2xl p-8 border border-white/20 inline-block">
              <div className="flex items-center justify-center space-x-8">
                <div className="text-center">
                  <div className="text-3xl font-bold gradient-text">10,000+</div>
                  <div className="text-gray-300">Certificates Issued</div>
                </div>
                <div className="w-px h-12 bg-white/20"></div>
                <div className="text-center">
                  <div className="text-3xl font-bold gradient-text">500+</div>
                  <div className="text-gray-300">Verified Students</div>
                </div>
                <div className="w-px h-12 bg-white/20"></div>
                <div className="text-center">
                  <div className="text-3xl font-bold gradient-text">99.9%</div>
                  <div className="text-gray-300">Uptime</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section - AI Agent Style */}
      <section id="features" className="py-24 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-green-900/10 to-transparent"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-white">Powered by</span> <span className="gradient-text">Advanced Technology</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Experience next-generation features designed for the modern digital economy
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="flex items-start space-x-6 group">
                <div className="w-14 h-14 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center flex-shrink-0 glow group-hover:scale-110 transition duration-300">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-3">Lightning-Fast Verification</h3>
                  <p className="text-gray-300 leading-relaxed">Advanced verification engine processes certificates in milliseconds with 99.99% accuracy.</p>
                </div>
              </div>

              <div className="flex items-start space-x-6 group">
                <div className="w-14 h-14 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center flex-shrink-0 glow group-hover:scale-110 transition duration-300">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-3">Blockchain Security</h3>
                  <p className="text-gray-300 leading-relaxed">Immutable certificate records stored on distributed ledger technology for ultimate security.</p>
                </div>
              </div>

              <div className="flex items-start space-x-6 group">
                <div className="w-14 h-14 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center flex-shrink-0 glow group-hover:scale-110 transition duration-300">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-3">Mobile-First Design</h3>
                  <p className="text-gray-300 leading-relaxed">Seamless experience across all devices with progressive web app capabilities.</p>
                </div>
              </div>

              <div className="flex items-start space-x-6 group">
                <div className="w-14 h-14 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-2xl flex items-center justify-center flex-shrink-0 glow group-hover:scale-110 transition duration-300">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-3">Social Sharing</h3>
                  <p className="text-gray-300 leading-relaxed">One-click sharing to LinkedIn, social media, and professional networks.</p>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="glass-strong rounded-3xl p-8 border border-white/20">
                <div className="aspect-w-16 aspect-h-12 bg-gradient-to-br from-green-900/50 to-emerald-900/50 rounded-2xl mb-6 flex items-center justify-center">
                  <Image
                    src="/img/certificate-illustration.svg"
                    alt="Advanced Verification"
                    width={400}
                    height={300}
                    className="rounded-2xl opacity-80"
                  />
                </div>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Verification Speed</span>
                    <span className="text-green-400 font-semibold">&lt; 100ms</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Security Level</span>
                    <span className="text-emerald-400 font-semibold">Military Grade</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-300">Global Reach</span>
                    <span className="text-green-400 font-semibold">190+ Countries</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Course Showcase Section - AI Agent Style */}
      <section id="courses" className="py-24 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="gradient-text">Master</span> <span className="text-white">In-Demand Skills</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Earn industry-recognized certificates in cutting-edge technologies
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {coursesLoading ? (
              // Loading skeleton
              Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="glass-strong rounded-2xl overflow-hidden border border-white/20 animate-pulse">
                  <div className="h-48 bg-gradient-to-br from-gray-600 to-gray-700"></div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-600 rounded mb-3"></div>
                    <div className="h-4 bg-gray-700 rounded mb-2"></div>
                    <div className="h-4 bg-gray-700 rounded mb-4 w-3/4"></div>
                    <div className="flex items-center justify-between">
                      <div className="h-6 bg-gray-600 rounded-full w-20"></div>
                      <div className="h-4 bg-gray-600 rounded w-16"></div>
                    </div>
                  </div>
                </div>
              ))
            ) : courses.length > 0 ? (
              courses.map((course, index) => {
                const colors = getCourseColors(index);
                const courseInitials = getCourseInitials(course.title);

                return (
                  <div key={course._id} className={`glass-strong rounded-2xl overflow-hidden border border-white/20 ${colors.border} transition duration-500 group`}>
                    <div className={`h-48 bg-gradient-to-br ${colors.gradient} flex items-center justify-center p-6 relative overflow-hidden`}>
                      <div className="absolute inset-0 bg-black/20"></div>
                      <div className={`w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 transition duration-500 relative z-10 shadow-lg ${index > 0 ? `delay-${index * 200}` : ''}`}>
                        <span className="text-white font-bold text-2xl">
                          {courseInitials}
                        </span>
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="font-bold text-xl mb-3 text-white">{course.title}</h3>
                      <p className="text-gray-300 text-sm mb-4 leading-relaxed line-clamp-2">{course.description}</p>
                      <div className="flex items-center justify-center">
                        <span className={`text-xs ${colors.badge} px-3 py-1 rounded-full`}>
                          {course.duration}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              // No courses available
              <div className="col-span-full text-center py-12">
                <div className="w-20 h-20 bg-gradient-to-r from-green-500/10 to-emerald-600/10 rounded-xl flex items-center justify-center mx-auto mb-6">
                  <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">No Courses Available</h3>
                <p className="text-gray-400">Check back soon for new courses!</p>
              </div>
            )}
          </div>

          <div className="text-center mt-16">
            <Link
              href="/courses"
              className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover inline-flex items-center space-x-2"
            >
              <span>View All Courses</span>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Verification Demo - AI Agent Style */}
      <section id="verify" className="py-24 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-green-900/20 via-emerald-900/20 to-teal-900/20"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-white">Experience</span> <span className="gradient-text">Instant Verification</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              See how our advanced verification system works in real-time
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="space-y-8">
              <div className="flex items-start space-x-6">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center flex-shrink-0 glow">
                  <span className="text-white font-bold text-lg">1</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-3">Submit Certificate ID</h3>
                  <p className="text-gray-300 leading-relaxed">Enter the unique certificate identifier or scan the QR code using any device.</p>
                </div>
              </div>

              <div className="flex items-start space-x-6">
                <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center flex-shrink-0 glow">
                  <span className="text-white font-bold text-lg">2</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-3">Advanced Verification Engine</h3>
                  <p className="text-gray-300 leading-relaxed">Our system processes the request through multiple security layers in milliseconds.</p>
                </div>
              </div>

              <div className="flex items-start space-x-6">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center flex-shrink-0 glow">
                  <span className="text-white font-bold text-lg">3</span>
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-white mb-3">Instant Results</h3>
                  <p className="text-gray-300 leading-relaxed">Get comprehensive verification results with student details and certificate authenticity.</p>
                </div>
              </div>

              <div className="pt-8">
                <Link
                  href="/verify"
                  className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover inline-flex items-center space-x-2"
                >
                  <span>Try Live Demo</span>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </Link>
              </div>
            </div>

            <div className="relative">
              <div className="glass-strong rounded-3xl p-8 border border-white/20">
                <div className="text-center mb-6">
                  <h4 className="text-xl font-bold text-white mb-2">Live Verification Demo</h4>
                  <p className="text-gray-300 text-sm">Sample certificate verification result</p>
                </div>

                <div className="bg-gradient-to-br from-green-900/50 to-emerald-900/50 rounded-2xl p-6 mb-6 flex items-center justify-center">
                  <Image
                    src="/img/qr-sample.svg"
                    alt="Sample QR Code"
                    width={150}
                    height={150}
                    className="opacity-80"
                  />
                </div>

                <div className="space-y-4">
                  <div className="glass rounded-xl p-4 border border-white/10">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-300">Certificate ID</span>
                      <span className="text-sm font-mono bg-green-500/20 text-green-300 px-3 py-1 rounded-lg">CB-2024-00123</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-300">Status</span>
                      <span className="text-sm bg-green-500/20 text-green-300 px-3 py-1 rounded-lg flex items-center">
                        <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                        Verified
                      </span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-300">Student</span>
                      <span className="text-sm text-white">John Doe</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-300">Course</span>
                      <span className="text-sm text-emerald-300">React.js Development</span>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="inline-flex items-center text-xs text-gray-400">
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                      </svg>
                      Verified in 47ms
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer - AI Agent Style */}
      <footer className="relative py-16 border-t border-white/10">
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 rounded-xl overflow-hidden glow">
                  <Image
                    src="/capalogo.png"
                    alt="CapacityBay Logo"
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <span className="text-2xl font-bold gradient-text">CapacityBay</span>
                  <div className="text-sm text-gray-400">Certificate Verification</div>
                </div>
              </div>
              <p className="text-gray-300 leading-relaxed mb-6 max-w-md">
                Empowering the next generation of developers with industry-recognized certifications
                and cutting-edge verification technology.
              </p>
              <div className="flex space-x-4">
                {/* Facebook */}
                <a href="https://www.facebook.com/CapacityBay1" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-blue-600/20 hover:text-blue-400 transition duration-300">
                  <svg className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                {/* X (Twitter) */}
                <a href="https://x.com/capacity_bay" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-gray-800/20 hover:text-gray-300 transition duration-300">
                  <svg className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
                {/* Instagram */}
                <a href="https://www.instagram.com/capacitybay1" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-pink-600/20 hover:text-pink-400 transition duration-300">
                  <svg className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </a>
                {/* LinkedIn */}
                <a href="https://www.linkedin.com/company/capacitybay-inc" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-blue-700/20 hover:text-blue-400 transition duration-300">
                  <svg className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                {/* YouTube */}
                <a href="https://www.youtube.com/@capacitybay" target="_blank" rel="noopener noreferrer" className="w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center hover:bg-red-600/20 hover:text-red-400 transition duration-300">
                  <svg className="w-5 h-5 text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                  </svg>
                </a>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-bold text-white mb-6">Quick Access</h4>
              <ul className="space-y-3">
                {session ? (
                  <li><Link href={session.user?.role === 'admin' ? '/admin/dashboard' : '/student/dashboard'} className="text-gray-300 hover:text-white transition duration-300 flex items-center space-x-2">
                    <span>Dashboard</span>
                  </Link></li>
                ) : (
                  <li><Link href="/login" className="text-gray-300 hover:text-white transition duration-300 flex items-center space-x-2">
                    <span>Student Portal</span>
                  </Link></li>
                )}
                <li><Link href="/verify" className="text-gray-300 hover:text-white transition duration-300 flex items-center space-x-2">
                  <span>Verify Certificate</span>
                </Link></li>
                <li><Link href="/admin/login" className="text-gray-300 hover:text-white transition duration-300 flex items-center space-x-2">
                  <span>Admin Login</span>
                </Link></li>
                <li><Link href="#courses" className="text-gray-300 hover:text-white transition duration-300 flex items-center space-x-2">
                  <span>Browse Courses</span>
                </Link></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-bold text-white mb-6">Support</h4>
              <ul className="space-y-3">
                <li className="text-gray-300">
                  <span className="text-sm">General Inquiries</span><br />
                  <a href="mailto:<EMAIL>" className="hover:text-white transition duration-300"><EMAIL></a>
                </li>
                <li className="text-gray-300">
                  <span className="text-sm">Certification Support</span><br />
                  <a href="mailto:<EMAIL>" className="hover:text-white transition duration-300"><EMAIL></a>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-white/10 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="text-gray-400 text-sm mb-4 md:mb-0">
                &copy; {new Date().getFullYear()} CapacityBay. All rights reserved.
              </div>
              <div className="flex items-center space-x-6 text-sm text-gray-400">
                <Link href="/privacy" className="hover:text-white transition duration-300">Privacy Policy</Link>
                <Link href="/terms" className="hover:text-white transition duration-300">Terms of Service</Link>
                <Link href="/security" className="hover:text-white transition duration-300">Security</Link>
              </div>
            </div>
          </div>
        </div>
      </footer>

    </div>
  );
}
