'use client';

import { useState, useEffect, FormEvent, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import LoginLayout from '@/components/auth/LoginLayout';
import PasswordInput from '@/components/ui/PasswordInput';
import Button from '@/components/ui/Button';
import Alert from '@/components/ui/Alert';

interface TokenValidation {
  valid: boolean;
  userType: string;
  userName: string;
  userEmail: string;
}

function ResetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const userType = searchParams.get('type');
  
  const [password, setPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [validating, setValidating] = useState<boolean>(true);
  const [message, setMessage] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [tokenData, setTokenData] = useState<TokenValidation | null>(null);

  useEffect(() => {
    const validateToken = async () => {
      if (!token || !userType) {
        setError('Invalid reset link. Please request a new password reset.');
        setValidating(false);
        return;
      }

      try {
        const response = await fetch(`/api/auth/reset-password?token=${token}&type=${userType}`);
        const data = await response.json();

        if (response.ok && data.valid) {
          setTokenData(data);
        } else {
          setError(data.error || 'Invalid or expired reset link.');
        }
      } catch (err) {
        setError('Network error. Please try again.');
        console.error('Token validation error:', err);
      } finally {
        setValidating(false);
      }
    };

    validateToken();
  }, [token, userType]);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    // Validate passwords
    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      setLoading(false);
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password,
          userType
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Password reset successful! Redirecting to login...');
        setTimeout(() => {
          router.push(userType === 'admin' ? '/admin/login' : '/login');
        }, 2000);
      } else {
        setError(data.error || 'An error occurred');
      }
    } catch (err) {
      setError('Network error. Please try again.');
      console.error('Reset password error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (validating) {
    return (
      <LoginLayout 
        title="Reset Password" 
        subtitle="Validating reset link..."
      >
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-300">Validating your reset link...</p>
        </div>
      </LoginLayout>
    );
  }

  if (!tokenData) {
    return (
      <LoginLayout 
        title="Invalid Reset Link" 
        subtitle="This reset link is invalid or has expired"
      >
        <div className="text-center py-8">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold text-white mb-4">Reset Link Invalid</h3>
          <p className="text-gray-300 mb-6">
            This password reset link is invalid, expired, or has already been used.
          </p>
          <div className="space-y-4">
            <Link 
              href="/forgot-password" 
              className="block bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition duration-300"
            >
              Request New Reset Link
            </Link>
            <Link 
              href="/" 
              className="block text-gray-400 hover:text-gray-300 transition-colors duration-300"
            >
              Return to Homepage
            </Link>
          </div>
        </div>
      </LoginLayout>
    );
  }

  const isAdmin = tokenData.userType === 'admin';

  return (
    <LoginLayout 
      title="Reset Password" 
      subtitle={`Set a new password for your ${isAdmin ? 'admin' : 'student'} account`}
    >
      <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
        {error && <Alert type="error" message={error} />}
        {message && <Alert type="success" message={message} />}
        
        <div className="space-y-4">
          <div className="text-center mb-6">
            <div className="bg-slate-800/50 p-4 rounded-lg">
              <p className="text-sm text-gray-300 mb-2">
                <span className="font-medium text-white">Account:</span> {tokenData.userName}
              </p>
              <p className="text-sm text-gray-300">
                <span className="font-medium text-white">Email:</span> {tokenData.userEmail}
              </p>
            </div>
          </div>

          <PasswordInput
            label="New Password"
            name="password"
            autoComplete="new-password"
            required
            placeholder="Enter your new password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            variant="auth"
          />
          
          <PasswordInput
            label="Confirm New Password"
            name="confirmPassword"
            autoComplete="new-password"
            required
            placeholder="Confirm your new password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            variant="auth"
          />

          <div className="text-xs text-gray-400 bg-slate-800/50 p-3 rounded-lg">
            <div className="flex items-center mb-2">
              <span className="text-blue-400 mr-2">💡</span>
              <span className="font-medium">Password Requirements</span>
            </div>
            <ul className="list-disc list-inside space-y-1">
              <li>At least 6 characters long</li>
              <li>Use a unique password you haven't used before</li>
              <li>Consider using a mix of letters, numbers, and symbols</li>
            </ul>
          </div>
        </div>

        <Button type="submit" isLoading={loading}>
          {loading ? 'Resetting Password...' : 'Reset Password'}
        </Button>
        
        <div className="text-center">
          <Link 
            href={isAdmin ? '/admin/login' : '/login'} 
            className="font-medium text-gray-400 hover:text-gray-300 transition-colors duration-300"
          >
            Back to Login
          </Link>
        </div>
      </form>
    </LoginLayout>
  );
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <LoginLayout title="Reset Password" subtitle="Loading...">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
        </div>
      </LoginLayout>
    }>
      <ResetPasswordForm />
    </Suspense>
  );
}
