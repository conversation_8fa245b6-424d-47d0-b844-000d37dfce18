import Link from 'next/link';

export default function SecurityPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-emerald-900">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]"></div>
      <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10"></div>

      {/* Navigation */}
      <nav className="glass border-b border-white/10 sticky top-0 z-50 backdrop-blur-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl overflow-hidden shadow-sm glow">
                <img
                  src="/capalogo.png"
                  alt="CapacityBay Logo"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <span className="text-xl font-bold text-white">CapacityBay</span>
                <div className="text-xs text-gray-300">Security</div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-gray-300 hover:text-white transition duration-300 px-4 py-2 rounded-lg hover:bg-white/10"
              >
                Return Home
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-12">
          <h1 className="text-5xl md:text-6xl font-bold mb-6">
            <span className="gradient-text">Security</span>
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
            Your security is our top priority. Learn about our comprehensive security measures and best practices.
          </p>
        </div>

        {/* Security Features Grid */}
        <div className="grid md:grid-cols-2 gap-6 mb-16">
          <div className="glass rounded-xl p-6 border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mb-4 shadow-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">End-to-End Encryption</h3>
            <p className="text-gray-300 text-sm">
              All data is encrypted in transit and at rest using industry-standard AES-256 encryption.
            </p>
          </div>

          <div className="glass rounded-xl p-6 border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center mb-4 shadow-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Secure Authentication</h3>
            <p className="text-gray-300 text-sm">
              Multi-factor authentication and secure session management protect your account.
            </p>
          </div>

          <div className="glass rounded-xl p-6 border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mb-4 shadow-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Certificate Integrity</h3>
            <p className="text-gray-300 text-sm">
              Digital signatures and cryptographic hashing ensure certificate authenticity and tamper-proofing.
            </p>
          </div>

          <div className="glass rounded-xl p-6 border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg flex items-center justify-center mb-4 shadow-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Real-time Monitoring</h3>
            <p className="text-gray-300 text-sm">
              24/7 security monitoring and automated threat detection protect against unauthorized access.
            </p>
          </div>
        </div>

        {/* Detailed Security Information */}
        <div className="glass rounded-2xl p-8 border border-white/20 shadow-2xl">
          <div className="prose prose-invert max-w-none">
            <div className="space-y-8 text-gray-300">
              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Data Protection</h2>
                <p className="mb-4">
                  We implement comprehensive data protection measures to safeguard your personal information and certification data:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Advanced encryption protocols for data transmission and storage</li>
                  <li>Regular security audits and penetration testing</li>
                  <li>Secure data centers with physical access controls</li>
                  <li>Automated backup systems with encrypted storage</li>
                  <li>Strict access controls and employee background checks</li>
                </ul>
              </div>

              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Certificate Security</h2>
                <p className="mb-4">
                  Our certificates are protected by multiple layers of security technology:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Unique cryptographic signatures for each certificate</li>
                  <li>QR codes with embedded verification data</li>
                  <li>Tamper-evident digital watermarks</li>
                  <li>Blockchain-based verification system</li>
                  <li>Real-time verification through our secure API</li>
                </ul>
              </div>

              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Account Security Best Practices</h2>
                <p className="mb-4">
                  Help us keep your account secure by following these recommendations:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>Use a strong, unique password for your account</li>
                  <li>Enable two-factor authentication when available</li>
                  <li>Keep your contact information up to date</li>
                  <li>Log out of shared or public computers</li>
                  <li>Report suspicious activity immediately</li>
                  <li>Regularly review your account activity</li>
                </ul>
              </div>

              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Compliance & Standards</h2>
                <p className="mb-4">
                  We adhere to industry-leading security standards and compliance frameworks:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>ISO 27001 Information Security Management</li>
                  <li>SOC 2 Type II compliance</li>
                  <li>GDPR and privacy regulation compliance</li>
                  <li>Regular third-party security assessments</li>
                  <li>Continuous security monitoring and improvement</li>
                </ul>
              </div>

              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Incident Response</h2>
                <p>
                  In the unlikely event of a security incident, we have established procedures to:
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4 mt-4">
                  <li>Immediately contain and assess the situation</li>
                  <li>Notify affected users within 72 hours</li>
                  <li>Work with law enforcement and security experts</li>
                  <li>Implement additional safeguards to prevent recurrence</li>
                  <li>Provide regular updates throughout the resolution process</li>
                </ul>
              </div>

              <div>
                <h2 className="text-2xl font-bold text-white mb-4">Security Contact</h2>
                <p>
                  If you discover a security vulnerability or have security concerns, please contact our security team immediately:
                </p>
                <div className="mt-4 p-4 bg-white/10 rounded-lg border border-white/20">
                  <p><strong>Security Email:</strong> <EMAIL></p>
                  <p><strong>Emergency Contact:</strong> Available 24/7 for critical security issues</p>
                  <p><strong>Response Time:</strong> We respond to security reports within 24 hours</p>
                </div>
              </div>

              <div className="text-sm text-gray-400 pt-8 border-t border-white/20">
                <p><strong>Last updated:</strong> {new Date().toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="glass rounded-2xl p-8 border border-white/20 text-center mt-16">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-8 h-8 rounded-lg overflow-hidden">
              <img
                src="/capalogo.png"
                alt="CapacityBay Logo"
                className="w-full h-full object-cover"
              />
            </div>
            <span className="text-xl font-bold gradient-text">CapacityBay</span>
          </div>
          <p className="text-gray-300 mb-6">Committed to the highest standards of security and data protection</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover"
            >
              Return to Homepage
            </Link>
            <Link
              href="/privacy"
              className="bg-white/10 text-white px-6 py-3 rounded-xl font-semibold hover:bg-white/20 transition duration-300 border border-white/20"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
