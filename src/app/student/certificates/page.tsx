'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ICourse, IStudent } from '@/lib/db/models';
import ModernStudentLayout from '@/components/student/ModernStudentLayout';

type StudentData = Omit<IStudent, 'password'> & {
  _id: string;
  courses: (Omit<ICourse, 'password'> & { _id: string })[];
};

export default function StudentCertificates() {
  const { data: session, status } = useSession({
    required: true,
    onUnauthenticated() {
      router.push('/login');
    },
  });
  const router = useRouter();
  const [studentData, setStudentData] = useState<StudentData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('newest');

  useEffect(() => {
    if (status !== 'authenticated' || !session) {
      return;
    }

    if (session.user?.role !== 'student') {
      router.push('/login');
      return;
    }

    async function fetchStudentData(): Promise<void> {
      try {
        const userId = session?.user?.id;
        if (!userId) {
          throw new Error('User ID not found in session');
        }

        const response = await fetch(`/api/students/${userId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch student data');
        }

        const data = await response.json();
        setStudentData(data.student);
      } catch (err: unknown) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching student data';
        setError(errorMessage);
        console.error('Error fetching student data:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchStudentData();
  }, [session, status, router]);

  // Function to generate course initials
  const getCourseInitials = (courseTitle: string): string => {
    // Remove common words and symbols
    const cleanTitle = courseTitle.replace(/&/g, '').trim();

    // Split by spaces and get first letter of each word
    const words = cleanTitle.split(/\s+/).filter(word => word.length > 0);

    if (words.length === 1) {
      // Single word: take first two letters
      return words[0].substring(0, 2).toUpperCase();
    } else {
      // Multiple words: take first letter of first two words
      return words.slice(0, 2).map(word => word.charAt(0)).join('').toUpperCase();
    }
  };

  // Filter and sort certificates
  const filteredCertificates = studentData?.courses.filter((course: any) =>
    course.courseTitle?.toLowerCase().includes(searchTerm.toLowerCase())
  ).sort((a: any, b: any) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      case 'oldest':
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      case 'name':
        return a.courseTitle?.localeCompare(b.courseTitle) || 0;
      default:
        return 0;
    }
  }) || [];

  if (status === 'loading' || loading) {
    return (
      <ModernStudentLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
        </div>
      </ModernStudentLayout>
    );
  }

  return (
    <ModernStudentLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
              </svg>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Certificates</h1>
              <p className="text-gray-600">View and manage all your earned certificates</p>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
              <div className="text-2xl font-bold text-gray-900">{studentData?.courses.length || 0}</div>
              <div className="text-sm text-gray-500">Total Certificates</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
              <div className="text-2xl font-bold text-gray-900">{filteredCertificates.length}</div>
              <div className="text-sm text-gray-500">Matching Search</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-100">
              <div className="text-2xl font-bold text-gray-900">
                {studentData?.courses.length ? new Date().getFullYear() - new Date((studentData.courses[0] as any).date).getFullYear() + 1 : 0}
              </div>
              <div className="text-sm text-gray-500">Years Learning</div>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Certificates
              </label>
              <input
                type="text"
                placeholder="Search by course name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sort By
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-300"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Course Name</option>
              </select>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-800 px-6 py-4 rounded-lg">
            <div className="flex items-center space-x-3">
              <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>{error}</span>
            </div>
          </div>
        )}

        {/* Certificates Grid */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          {filteredCertificates.length === 0 ? (
            <div className="p-12 text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-green-500/10 to-emerald-600/10 rounded-xl flex items-center justify-center mx-auto mb-6">
                <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {searchTerm ? 'No matching certificates found' : 'No certificates yet'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm 
                  ? 'Try adjusting your search terms or browse all certificates.' 
                  : 'Start your learning journey to earn your first certificate!'
                }
              </p>
              <Link
                href="/#courses"
                className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 shadow-sm inline-flex items-center space-x-2"
              >
                <span>Browse Courses</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
              </Link>
            </div>
          ) : (
            <div className="p-6">
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {filteredCertificates.map((course: any) => (
                  <div key={String(course._id)} className="bg-gray-50 rounded-lg overflow-hidden border border-gray-200 hover:border-green-200 hover:shadow-md transition-all duration-300 group">
                    <div className="h-40 bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center p-6 relative overflow-hidden">
                      <div className="w-20 h-20 bg-white/20 rounded-xl flex items-center justify-center text-white font-bold text-2xl transform group-hover:scale-110 transition duration-300 relative z-10 shadow-lg">
                        {getCourseInitials(course.courseTitle)}
                      </div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{course.courseTitle}</h3>
                      <p className="text-gray-600 text-sm mb-4">
                        Issued on: {new Date(course.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                      <div className="flex space-x-3">
                        <Link
                          href={`/verify/${course._id}`}
                          className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-4 py-2 rounded-lg font-medium hover:from-green-600 hover:to-emerald-700 transition duration-300 text-center shadow-sm"
                          target="_blank"
                        >
                          Verify
                        </Link>
                        <Link
                          href={`/api/certificates/download/${course._id}`}
                          className="flex-1 bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-300 transition duration-300 text-center"
                        >
                          Download
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </ModernStudentLayout>
  );
}
