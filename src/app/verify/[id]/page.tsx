'use client';

import { useState, useEffect, use } from 'react';
import Link from 'next/link';

type VerificationResult = {
  verified: boolean;
  student: {
    name: string;
    email: string;
  };
  course: {
    title: string;
    description: string;
    badgeImage: string;
  };
  certificate: {
    id: string;
    issueDate: string;
    downloadCount: number;
    shareCount: number;
  };
};

export default function VerifyPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  const [result, setResult] = useState<VerificationResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function verifyCertificate() {
      try {
        const response = await fetch(`/api/verify/${resolvedParams.id}`);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to verify certificate');
        }

        const data = await response.json();
        setResult(data);

        // Update document title dynamically
        if (data && data.student && data.course) {
          document.title = `Certificate Verification - ${data.student.name} - ${data.course.title}`;
        }
      } catch (err) {
        setError((err as Error).message || 'An error occurred while verifying the certificate');
        document.title = 'Certificate Verification - CapacityBay';
      } finally {
        setLoading(false);
      }
    }

    verifyCertificate();
  }, [resolvedParams.id]);

  // Function to generate course initials
  const getCourseInitials = (courseTitle: string): string => {
    // Remove common words and symbols
    const cleanTitle = courseTitle.replace(/&/g, '').trim();

    // Split by spaces and get first letter of each word
    const words = cleanTitle.split(/\s+/).filter(word => word.length > 0);

    if (words.length === 1) {
      // Single word: take first two letters
      return words[0].substring(0, 2).toUpperCase();
    } else {
      // Multiple words: take first letter of first two words
      return words.slice(0, 2).map(word => word.charAt(0)).join('').toUpperCase();
    }
  };

  // Helper function to get detailed course title
  const getDetailedCourseTitle = (courseTitle: string) => {
    const title = courseTitle.toUpperCase();
    const titleMap: Record<string, string> = {
      'HTML': 'Hyper Text Markup Language (HTML)',
      'CSS': 'Cascading Style Sheet (CSS)',
      'JAVASCRIPT': 'JavaScript Programming',
      'PYTHON': 'Python Programming',
      'NODEJS': 'Node.js Runtime',
      'EXPRESSJS': 'Express.js Framework',
      'REACTJS': 'React.js Library',
      'MONGODB': 'MongoDB Database',
      'BOOTSTRAP': 'Bootstrap (CSS) Framework',
      'GITHUB': 'GitHub Version Control',
      // Add more mappings as needed
    };
    
    return titleMap[title] || courseTitle;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-green-400 mx-auto mb-4"></div>
          <p className="text-gray-300 text-lg">Verifying certificate...</p>
        </div>
      </div>
    );
  }

  if (error || !result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 relative overflow-hidden flex items-center justify-center">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-4000"></div>
        </div>

        <div className="max-w-md mx-auto text-center p-8 relative z-10">
          <div className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6 border border-red-500/30">
            <svg className="w-10 h-10 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>

          <h1 className="text-2xl font-bold text-white mb-4">
            Certificate Not Found
          </h1>

          <p className="text-gray-300 mb-6">
            {error || 'The certificate could not be verified. It may not exist or has been revoked.'}
          </p>

          <p className="text-gray-400 text-sm mb-8">
            Please report any plagiarism to <a href="mailto:<EMAIL>" className="text-green-400 hover:text-green-300 transition-colors"><EMAIL></a>
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 shadow-sm"
            >
              Return Home
            </Link>
            <button
              onClick={() => window.location.reload()}
              className="glass text-white px-6 py-3 rounded-lg font-semibold hover:bg-white/20 transition duration-300 border border-white/20"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  const { student, course, certificate } = result;
  const detailedCourseTitle = getDetailedCourseTitle(course.title);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-green-400 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-4000"></div>
        </div>

      {/* Navigation */}
      <nav className="glass border-b border-white/10 sticky top-0 z-50 backdrop-blur-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl overflow-hidden shadow-sm glow">
                <img
                  src="/capalogo.png"
                  alt="CapacityBay Logo"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <span className="text-xl font-bold text-white">CapacityBay</span>
                <div className="text-xs text-gray-300">Certificate Verification</div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-gray-300 hover:text-white transition duration-300 px-4 py-2 rounded-lg hover:bg-white/10"
              >
                Return Home
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="py-12 px-4 relative z-10">
        <div className="max-w-5xl mx-auto">
          {/* Verification Status Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-6 py-3 bg-green-500/20 rounded-full border border-green-400/30 mb-6 backdrop-blur-sm">
              <svg className="w-6 h-6 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span className="text-green-300 font-semibold">Certificate Verified Successfully</span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              Authentic Certificate
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              This certificate has been verified as genuine and issued by CapacityBay
            </p>
          </div>

          {/* Main Certificate Card */}
          <div className="glass-strong rounded-xl p-8 md:p-12 shadow-lg border border-white/20 mb-8 backdrop-blur-md">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              {/* Badge Section */}
              <div className="text-center">
                <div className="relative inline-block">
                  <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-2xl p-8 border border-green-400/30 backdrop-blur-sm">
                    <div className="w-48 h-48 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center text-white font-bold text-6xl mx-auto shadow-2xl">
                      {getCourseInitials(course.title)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Certificate Details */}
              <div className="space-y-6">
                <div>
                  <h2 className="text-3xl md:text-4xl font-bold text-white mb-2">{student.name}</h2>
                  <p className="text-gray-300 text-lg mb-4">has successfully completed</p>
                  <h3 className="text-2xl font-bold text-green-400 mb-6">{detailedCourseTitle}</h3>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="glass rounded-lg p-4 border border-white/20 backdrop-blur-sm">
                    <div className="text-sm text-gray-400 mb-1">Issue Date</div>
                    <div className="text-white font-semibold">{new Date(certificate.issueDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</div>
                  </div>
                  <div className="glass rounded-lg p-4 border border-white/20 backdrop-blur-sm">
                    <div className="text-sm text-gray-400 mb-1">Certificate ID</div>
                    <div className="text-white font-mono text-sm break-all">{resolvedParams.id}</div>
                  </div>
                </div>

                <div className="glass rounded-lg p-4 border border-white/20 backdrop-blur-sm">
                  <div className="text-sm text-gray-400 mb-1">Student Email</div>
                  <div className="text-white">{student.email}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Course Description */}
          {course.description && (
            <div className="glass-strong rounded-2xl p-8 border border-white/20 mb-8">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center glow">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                  </svg>
                </div>
                <div>
                  <h4 className="text-2xl font-bold text-white">Course Overview</h4>
                  <p className="text-gray-300">What this certification covers</p>
                </div>
              </div>
              <p className="text-gray-300 leading-relaxed text-lg">{course.description}</p>
            </div>
          )}

          {/* Verification Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="glass-strong rounded-2xl p-6 border border-white/20 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4 glow">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
              </div>
              <h5 className="text-lg font-bold text-white mb-2">Secure Verification</h5>
              <p className="text-gray-300 text-sm">Blockchain-secured certificate with tamper-proof verification</p>
            </div>

            <div className="glass-strong rounded-2xl p-6 border border-white/20 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mx-auto mb-4 glow">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <h5 className="text-lg font-bold text-white mb-2">Instant Verification</h5>
              <p className="text-gray-300 text-sm">Real-time verification powered by AI technology</p>
            </div>

            <div className="glass-strong rounded-2xl p-6 border border-white/20 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-4 glow">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h5 className="text-lg font-bold text-white mb-2">Global Recognition</h5>
              <p className="text-gray-300 text-sm">Internationally accepted by leading employers</p>
            </div>
          </div>

          {/* Footer */}
          <div className="glass rounded-2xl p-8 border border-white/20 text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <div className="w-8 h-8 rounded-lg overflow-hidden">
                <img
                  src="/capalogo.png"
                  alt="CapacityBay Logo"
                  className="w-full h-full object-cover"
                />
              </div>
              <span className="text-xl font-bold gradient-text">CapacityBay</span>
            </div>
            <p className="text-gray-300 mb-6">Powered by CapacityBay Certificate Verification System</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/"
                className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover"
              >
                Return to Homepage
              </Link>
              <Link
                href="/#verify"
                className="glass text-white px-6 py-3 rounded-xl font-semibold hover:bg-white/20 transition duration-300 border border-white/20"
              >
                Verify Another Certificate
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}