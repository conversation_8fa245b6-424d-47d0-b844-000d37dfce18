'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';

export default function MobileMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const { data: session } = useSession();

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="md:hidden flex items-center">
        <button 
          onClick={toggleMenu}
          className="outline-none text-gray-300 hover:text-white transition duration-300"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7"></path>
          </svg>
        </button>
      </div>

      {/* Mobile Menu Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={toggleMenu}></div>
          <div className="fixed top-0 right-0 h-full w-full glass-strong border-l border-white/20 p-6">
            <div className="flex justify-between items-center mb-8">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-lg overflow-hidden glow">
                  <img
                    src="/capalogo.png"
                    alt="CapacityBay Logo"
                    className="w-full h-full object-cover"
                  />
                </div>
                <span className="text-lg font-bold gradient-text">CapacityBay</span>
              </div>
              <button 
                onClick={toggleMenu}
                className="text-gray-300 hover:text-white transition duration-300"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <nav className="space-y-4 bg-white rounded-xl p-4 w-full backdrop-blur-sm">
              <Link 
                href="#about" 
                onClick={toggleMenu}
                className="block py-3 text-emerald-600 hover:text-green-500 transition duration-300 border-b border-emerald-600/10"
              >
                About
              </Link>
              <Link 
                href="#features" 
                onClick={toggleMenu}
                className="block py-3 text-emerald-600 hover:text-green-500 transition duration-300 border-b border-emerald-600/10"
              >
                Features
              </Link>
              <Link 
                href="#courses" 
                onClick={toggleMenu}
                className="block py-3 text-emerald-600 hover:text-green-500 transition duration-300 border-b border-emerald-600/10"
              >
                Courses
              </Link>
              <Link 
                href="#verify" 
                onClick={toggleMenu}
                className="block py-3 text-emerald-600 hover:text-green-500 transition duration-300 border-b border-emerald-600/10"
              >
                Verify
              </Link>

              <div className="mt-8 space-y-4">
              {session ? (
                <Link
                  href={session.user?.role === 'admin' ? '/admin/dashboard' : '/student/dashboard'}
                  onClick={toggleMenu}
                  className="block w-full text-center py-3 px-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  href="/login"
                  onClick={toggleMenu}
                  className="block w-full text-center py-3 px-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg hover:from-green-600 hover:to-emerald-700 transition duration-300 glow-hover"
                >
                  Student Portal
                </Link>
              )}
            </div>
            </nav>
            
            
          </div>
        </div>
      )}
    </>
  );
}
