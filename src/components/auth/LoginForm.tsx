'use client';

import { useState, FormEvent } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Input from '../ui/Input';
import Button from '../ui/Button';
import Alert from '../ui/Alert';

interface LoginFormProps {
  userType: 'admin' | 'student';
  redirectPath: string;
}

const LoginForm: React.FC<LoginFormProps> = ({ userType, redirectPath }) => {
  const router = useRouter();
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // First, validate the user type and credentials manually
      const validateResponse = await fetch('/api/auth/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          userType
        }),
      });

      const validateData = await validateResponse.json();

      if (!validateResponse.ok) {
        setError(validateData.error || 'Authentication failed');
        return;
      }

      // If validation passes, proceed with NextAuth sign in
      const result = await signIn('credentials', {
        redirect: false,
        email,
        password,
        userType
      });

      if (result?.error) {
        setError('Authentication failed. Please try again.');
      } else {
        router.push(redirectPath);
      }
    } catch (err) {
      setError('An error occurred during login');
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
      {error && <Alert type="error" message={error} />}
      
      <div className="space-y-4">
        <Input
          label="Email address"
          name="email"
          type="email"
          autoComplete="email"
          required
          placeholder="Enter your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
        />
        
        <Input
          label="Password"
          name="password"
          type="password"
          autoComplete="current-password"
          required
          placeholder="Enter your password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
      </div>

      <Button type="submit" isLoading={loading}>
        Sign in
      </Button>

      <div className="text-center space-y-4">
        <Link
          href={`/forgot-password?type=${userType}`}
          className="block font-medium text-blue-400 hover:text-blue-300 transition-colors duration-300"
        >
          Forgot your password?
        </Link>

        <Link href="/" className="block font-medium text-green-400 hover:text-green-300 transition-colors duration-300">
          Return to Homepage
        </Link>
      </div>
    </form>
  );
};

export default LoginForm;