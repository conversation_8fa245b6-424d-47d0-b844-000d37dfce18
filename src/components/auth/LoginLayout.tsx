import React, { ReactNode } from 'react';

interface LoginLayoutProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
}

const LoginLayout: React.FC<LoginLayoutProps> = ({ children, title, subtitle }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-slate-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-green-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-emerald-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-teal-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-500"></div>
      </div>

      <div className="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-md w-full space-y-8">
          {/* Logo and Back to Home */}
          <div className="text-center mb-8">
            <a href="/" className="inline-flex items-center space-x-2 text-gray-300 hover:text-white transition duration-300 mb-8">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
              </svg>
              <span>Back to Home</span>
            </a>
          </div>

          {/* Login Card */}
          <div className="glass-strong rounded-3xl p-10 border border-white/20 relative overflow-hidden">
            <div className="text-center mb-8">
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center glow float-animation">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
              </div>
              <h2 className="text-3xl font-bold gradient-text mb-2">
                {title}
              </h2>
              {subtitle && (
                <p className="text-gray-300">
                  {subtitle}
                </p>
              )}
            </div>

            {children}

            {/* Floating decorative elements */}
            <div className="absolute top-4 right-4 w-12 h-12 bg-green-500/20 rounded-full blur-lg float-animation"></div>
            <div className="absolute bottom-4 left-4 w-8 h-8 bg-emerald-500/20 rounded-lg rotate-45 blur-sm float-animation delay-1000"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginLayout;