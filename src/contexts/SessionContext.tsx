'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { Session } from 'next-auth';

interface SessionContextType {
  session: Session | null;
  status: 'loading' | 'authenticated' | 'unauthenticated';
  isLoading: boolean;
}

const SessionContext = createContext<SessionContextType>({
  session: null,
  status: 'loading',
  isLoading: true,
});

export const useOptimizedSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useOptimizedSession must be used within a SessionProvider');
  }
  return context;
};

interface SessionProviderProps {
  children: ReactNode;
}

export const OptimizedSessionProvider = ({ children }: SessionProviderProps) => {
  const { data: session, status } = useSession();
  const [cachedSession, setCachedSession] = useState<Session | null>(null);
  const [cachedStatus, setCachedStatus] = useState<'loading' | 'authenticated' | 'unauthenticated'>('loading');
  const [lastUpdate, setLastUpdate] = useState<number>(0);

  useEffect(() => {
    const now = Date.now();
    // Only update if it's been more than 5 seconds since last update or status changed
    if (now - lastUpdate > 5000 || status !== cachedStatus) {
      setCachedSession(session);
      setCachedStatus(status);
      setLastUpdate(now);
    }
  }, [session, status, lastUpdate, cachedStatus]);

  const value: SessionContextType = {
    session: cachedSession,
    status: cachedStatus,
    isLoading: cachedStatus === 'loading',
  };

  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
};
