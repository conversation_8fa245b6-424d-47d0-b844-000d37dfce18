import mongoose, { Schema, Document, Model } from 'mongoose';

// TypeScript interfaces
export interface ICourse extends Document {
  title: string;
  description: string;
  duration: string;
  badgeImage: string;
  certificateTemplate?: string;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICertificate extends Document {
  studentId: mongoose.Types.ObjectId;
  courseId: mongoose.Types.ObjectId;
  issueDate: Date;
  certificateId?: string; // Unique identifier for verification (optional for legacy certificates)
  downloadCount: number;
  shareCount: number;
  isRevoked: boolean;
  isLegacy?: boolean; // Flag to identify legacy certificates
  createdAt: Date;
  updatedAt: Date;
}

export interface IStudent extends Document {
  name: string;
  email: string;
  password?: string;
  role: string;
  profilePicture?: string;
  joinDate: Date;
  lastLogin?: Date;
  isActive: boolean;
  courses: mongoose.Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IAdmin extends Document {
  name: string;
  username: string;
  password: string;
  email: string;
  role: string;
  permissions: string[];
  lastLogin?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IPasswordResetToken extends Document {
  userId: mongoose.Types.ObjectId;
  userType: 'student' | 'admin';
  token: string;
  expiresAt: Date;
  used: boolean;
  createdAt: Date;
}

// Mongoose schemas
const courseSchema = new Schema<ICourse>({
  title: { type: String, required: true },
  description: { type: String, required: true },
  duration: { type: String, required: true },
  badgeImage: { type: String, required: true },
  certificateTemplate: { type: String },
  active: { type: Boolean, default: true }
}, {
  timestamps: true
});

const certificateSchema = new Schema<ICertificate>({
  studentId: { type: Schema.Types.ObjectId, ref: 'Student', required: true },
  courseId: { type: Schema.Types.ObjectId, ref: 'Course', required: true },
  issueDate: { type: Date, required: true },
  certificateId: {
    type: String,
    required: false, // Make optional for legacy certificates
    unique: true,
    sparse: true, // Allow null values to be non-unique
    index: { unique: true, sparse: true } // Ensure sparse index
  },
  downloadCount: { type: Number, default: 0 },
  shareCount: { type: Number, default: 0 },
  isRevoked: { type: Boolean, default: false },
  isLegacy: { type: Boolean, default: false } // Flag for legacy certificates
}, {
  timestamps: true
});

const studentSchema = new Schema<IStudent>({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String },
  role: { type: String, default: 'student' },
  profilePicture: { type: String },
  joinDate: { type: Date, default: Date.now },
  lastLogin: { type: Date },
  isActive: { type: Boolean, default: true },
  courses: [{ type: Schema.Types.ObjectId, ref: 'Course' }]
}, {
  timestamps: true
});

const adminSchema = new Schema<IAdmin>({
  name: { type: String, required: true },
  username: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  role: { type: String, default: 'admin' },
  permissions: [{ type: String }],
  lastLogin: { type: Date },
  isActive: { type: Boolean, default: true }
}, {
  timestamps: true
});

const passwordResetTokenSchema = new Schema<IPasswordResetToken>({
  userId: { type: Schema.Types.ObjectId, required: true },
  userType: { type: String, enum: ['student', 'admin'], required: true },
  token: { type: String, required: true, unique: true },
  expiresAt: { type: Date, required: true },
  used: { type: Boolean, default: false }
}, {
  timestamps: true
});

// Add indexes for better performance (excluding unique fields which are auto-indexed)
courseSchema.index({ title: 1 });
courseSchema.index({ active: 1 });

certificateSchema.index({ studentId: 1 });
certificateSchema.index({ courseId: 1 });
certificateSchema.index({ isRevoked: 1 });

studentSchema.index({ isActive: 1 });

adminSchema.index({ isActive: 1 });

// Note: token field already has unique: true in schema, so no need for additional index
passwordResetTokenSchema.index({ expiresAt: 1 });
passwordResetTokenSchema.index({ userId: 1, userType: 1 });

// Models
export const Course = mongoose.models.Course || mongoose.model<ICourse>('Course', courseSchema);
export const Certificate = mongoose.models.Certificate || mongoose.model<ICertificate>('Certificate', certificateSchema);
export const Student = mongoose.models.Student || mongoose.model<IStudent>('Student', studentSchema);
export const Admin = mongoose.models.Admin || mongoose.model<IAdmin>('Admin', adminSchema);
export const PasswordResetToken = mongoose.models.PasswordResetToken || mongoose.model<IPasswordResetToken>('PasswordResetToken', passwordResetTokenSchema);