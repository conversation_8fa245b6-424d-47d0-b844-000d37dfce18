import nodemailer from 'nodemailer';
import { generateCertificateEmailHTML, generatePasswordResetEmailHTML } from './email-template';

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  tls: {
        rejectUnauthorized: boolean,
      };
}

interface CertificateEmailData {
  studentName: string;
  studentEmail: string;
  courseName: string;
  certificateId: string;
  issueDate: string;
  verificationUrl: string;
}

interface PasswordResetEmailData {
  userName: string;
  userEmail: string;
  resetUrl: string;
  userType: 'student' | 'admin';
}

export class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private isConfigured: boolean = false;

  constructor() {
    // Check if email configuration is available and not using placeholder values
    const hasValidCredentials = process.env.SMTP_USER &&
                                process.env.SMTP_PASS &&
                                !process.env.SMTP_USER.includes('your-email') &&
                                !process.env.SMTP_PASS.includes('your-app-password');

    if (hasValidCredentials) {
      try {
        // Configure email transporter
        const emailConfig: EmailConfig = {
          host: "*************",
      port: 465,
      secure: true,
      auth: {
        user: "<EMAIL>",
        pass: "dV*;hZ-sHYK$",
      },
      tls: {
        rejectUnauthorized: false,
      },
        };

      

        this.transporter = nodemailer.createTransport(emailConfig);
        this.isConfigured = true;
        console.log('✅ Email service configured successfully with real credentials');
      } catch (error) {
        console.warn('❌ Failed to configure email service:', error);
        this.isConfigured = false;
      }
    } else {
      console.warn('⚠️  Email service not configured - please update .env.local with real SMTP credentials');
      console.warn('📧 To enable email sending:');
      console.warn('   1. Update SMTP_USER with your real email address');
      console.warn('   2. Update SMTP_PASS with your real app password');
      console.warn('   3. For Gmail: Enable 2FA and generate an App Password');
      this.isConfigured = false;
    }
  }

  async sendCertificateEmail(
    emailData: CertificateEmailData,
    certificatePdfBuffer: Buffer
  ): Promise<boolean> {
    if (!this.isConfigured || !this.transporter) {
      console.warn('📧 Email service not configured - skipping email send');
      console.warn('💡 To enable email sending, update your .env.local file with real SMTP credentials');
      return false;
    }

    try {
      // Generate email HTML
      const emailHTML = generateCertificateEmailHTML(emailData);

      // Email options
      const mailOptions = {
        from: {
          name: 'CapacityBay Certification',
          address: '<EMAIL>',
        },
        to: emailData.studentEmail,
        subject: `🎉 Your Certificate is Ready - ${emailData.courseName}`,
        html: emailHTML,
        attachments: [
          {
            filename: `CapacityBay-Certificate-${emailData.certificateId}.pdf`,
            content: certificatePdfBuffer,
            contentType: 'application/pdf',
          },
        ],
      };

      // Send email
      const info = await this.transporter.sendMail(mailOptions);
      console.log('✅ Certificate email sent successfully to:', emailData.studentEmail);
      console.log('📧 Message ID:', info.messageId);
      return true;
    } catch (error: any) {
      console.error('❌ Error sending certificate email to:', emailData.studentEmail);

      // Provide specific error guidance
      if (error.code === 'ETIMEDOUT' || error.command === 'CONN') {
        console.error('🔌 Connection timeout - check your SMTP credentials and network connection');
        console.error('💡 For Gmail: Make sure you\'re using an App Password, not your regular password');
      } else if (error.code === 'EAUTH') {
        console.error('🔐 Authentication failed - check your SMTP_USER and SMTP_PASS credentials');
      } else {
        console.error('📧 Email error details:', error.message);
      }

      return false;
    }
  }

  async sendPasswordResetEmail(emailData: PasswordResetEmailData): Promise<boolean> {
    if (!this.isConfigured || !this.transporter) {
      console.warn('📧 Email service not configured - skipping password reset email');
      console.warn('💡 To enable email sending, update your .env.local file with real SMTP credentials');
      return false;
    }

    try {
      // Generate email HTML
      const emailHTML = generatePasswordResetEmailHTML(emailData);

      // Email options
      const mailOptions = {
        from: {
          name: 'CapacityBay Security',
          address: process.env.SMTP_FROM || process.env.SMTP_USER || '<EMAIL>',
        },
        to: emailData.userEmail,
        subject: `🔐 Reset Your Password - CapacityBay ${emailData.userType === 'admin' ? 'Admin' : 'Student'} Account`,
        html: emailHTML,
      };

      // Send email
      const info = await this.transporter.sendMail(mailOptions);
      console.log('✅ Password reset email sent successfully to:', emailData.userEmail);
      console.log('📧 Message ID:', info.messageId);
      return true;
    } catch (error: any) {
      console.error('❌ Error sending password reset email to:', emailData.userEmail);

      // Provide specific error guidance
      if (error.code === 'ETIMEDOUT' || error.command === 'CONN') {
        console.error('🔌 Connection timeout - check your SMTP credentials and network connection');
        console.error('💡 For Gmail: Make sure you\'re using an App Password, not your regular password');
      } else if (error.code === 'EAUTH') {
        console.error('🔐 Authentication failed - check your SMTP_USER and SMTP_PASS credentials');
      } else {
        console.error('📧 Email error details:', error.message);
      }

      return false;
    }
  }

  async verifyConnection(): Promise<boolean> {
    if (!this.isConfigured || !this.transporter) {
      console.warn('Email service not configured - cannot verify connection');
      return false;
    }

    try {
      await this.transporter.verify();
      console.log('Email service connection verified successfully');
      return true;
    } catch (error) {
      console.error('Email service connection failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const emailService = new EmailService();
