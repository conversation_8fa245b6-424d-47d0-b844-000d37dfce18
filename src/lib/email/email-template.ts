interface CertificateEmailData {
  studentName: string;
  studentEmail: string;
  courseName: string;
  certificateId: string;
  issueDate: string;
  verificationUrl: string;
}

interface PasswordResetEmailData {
  userName: string;
  userEmail: string;
  resetUrl: string;
  userType: 'student' | 'admin';
}

export function generateCertificateEmailHTML(data: CertificateEmailData): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Certificate is Ready!</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f8fafc;
        }
        
        .email-container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
          padding: 40px 30px;
          text-align: center;
          color: white;
        }
        
        .logo {
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          margin: 0 auto 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          font-weight: bold;
        }
        
        .header h1 {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
        }
        
        .header p {
          font-size: 16px;
          opacity: 0.9;
        }
        
        .content {
          padding: 40px 30px;
        }
        
        .greeting {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 20px;
        }
        
        .message {
          font-size: 16px;
          color: #4b5563;
          margin-bottom: 30px;
          line-height: 1.7;
        }
        
        .certificate-info {
          background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
          border: 1px solid #bbf7d0;
          border-radius: 12px;
          padding: 25px;
          margin: 30px 0;
        }
        
        .certificate-info h3 {
          color: #15803d;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          display: flex;
          align-items: center;
        }
        
        .certificate-info h3::before {
          content: "🎓";
          margin-right: 8px;
        }
        
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px;
          margin-top: 20px;
        }
        
        .info-item {
          background: white;
          padding: 15px;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }
        
        .info-label {
          font-size: 12px;
          font-weight: 600;
          color: #6b7280;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 5px;
        }
        
        .info-value {
          font-size: 14px;
          font-weight: 600;
          color: #1f2937;
        }
        
        .certificate-id {
          font-family: 'Courier New', monospace;
          background: #f3f4f6;
          padding: 2px 6px;
          border-radius: 4px;
        }
        
        .cta-section {
          text-align: center;
          margin: 40px 0;
        }
        
        .cta-button {
          display: inline-block;
          background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
          color: white;
          text-decoration: none;
          padding: 16px 32px;
          border-radius: 8px;
          font-weight: 600;
          font-size: 16px;
          box-shadow: 0 4px 6px rgba(34, 197, 94, 0.3);
          transition: transform 0.2s ease;
        }
        
        .cta-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 12px rgba(34, 197, 94, 0.4);
        }
        
        .verification-section {
          background: #f8fafc;
          border-radius: 8px;
          padding: 20px;
          margin: 30px 0;
          text-align: center;
        }
        
        .verification-section h4 {
          color: #1f2937;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 10px;
        }
        
        .verification-url {
          color: #059669;
          font-size: 14px;
          word-break: break-all;
        }
        
        .footer {
          background: #f8fafc;
          padding: 30px;
          text-align: center;
          border-top: 1px solid #e5e7eb;
        }
        
        .footer-logo {
          font-size: 20px;
          font-weight: 700;
          color: #22c55e;
          margin-bottom: 10px;
        }
        
        .footer-text {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 20px;
        }
        
        .social-links {
          display: flex;
          justify-content: center;
          gap: 15px;
          margin-bottom: 20px;
        }
        
        .social-link {
          width: 36px;
          height: 36px;
          background: #e5e7eb;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          text-decoration: none;
          color: #6b7280;
          transition: all 0.2s ease;
        }
        
        .social-link:hover {
          background: #22c55e;
          color: white;
        }
        
        .footer-links {
          font-size: 12px;
          color: #9ca3af;
        }
        
        .footer-links a {
          color: #059669;
          text-decoration: none;
          margin: 0 10px;
        }
        
        @media (max-width: 600px) {
          .email-container {
            margin: 0;
            border-radius: 0;
          }
          
          .header, .content, .footer {
            padding: 30px 20px;
          }
          
          .info-grid {
            grid-template-columns: 1fr;
          }
          
          .social-links {
            flex-wrap: wrap;
          }
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="header">
          <div class="logo"><img src="https://capacitybay.org/capalogo.png" alt="Logo" width="60"></div>
          <h1>Congratulations!</h1>
          <p>Your certificate is ready for download</p>
        </div>
        
        <!-- Content -->
        <div class="content">
          <div class="greeting">Hello ${data.studentName},</div>
          
          <div class="message">
            We're excited to inform you that your certificate for <strong>${data.courseName}</strong> has been successfully issued! 
            This achievement represents your dedication to learning and professional growth.
          </div>
          
          <!-- Certificate Information -->
          <div class="certificate-info">
            <h3>Certificate Details</h3>
            <p style="color: #4b5563; margin-bottom: 20px;">
              Your certificate is attached to this email as a PDF file. You can also verify its authenticity online.
            </p>
            
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">Course Name</div>
                <div class="info-value">${data.courseName}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Issue Date</div>
                <div class="info-value">${data.issueDate}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Certificate ID</div>
                <div class="info-value certificate-id">${data.certificateId}</div>
              </div>
              <div class="info-item">
                <div class="info-label">Status</div>
                <div class="info-value" style="color: #059669;">✓ Verified</div>
              </div>
            </div>
          </div>
          
          <!-- Call to Action -->
          <div class="cta-section">
            <a href="${data.verificationUrl}" class="cta-button">
              🔍 Verify Certificate Online
            </a>
          </div>
          
          <!-- Verification Section -->
          <div class="verification-section">
            <h4>🔐 Certificate Verification</h4>
            <p style="color: #6b7280; font-size: 14px; margin-bottom: 10px;">
              Anyone can verify the authenticity of your certificate using this link:
            </p>
            <div class="verification-url">${data.verificationUrl}</div>
          </div>
          
          <div class="message">
            <strong>What's Next?</strong><br>
            • Add your certificate to your LinkedIn profile<br>
            • Share your achievement on social media<br>
            • Include it in your professional portfolio<br>
            • Continue learning with our advanced courses
          </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
          <div class="footer-logo">CapacityBay</div>
          <div class="footer-text">
            Empowering the next generation of developers with industry-recognized certifications.
          </div>
          
          <div class="footer-links">
            <a href="https://certverify.capacitybay.org/privacy">Privacy Policy</a> |
            <a href="https://certverify.capacitybay.org/terms">Terms of Service</a> |
            <a href="mailto:<EMAIL>">Support</a>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}

export function generatePasswordResetEmailHTML(data: PasswordResetEmailData): string {
  const userTypeDisplay = data.userType === 'admin' ? 'Administrator' : 'Student';
  const brandColor = data.userType === 'admin' ? '#dc2626' : '#22c55e';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reset Your Password</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333333;
          background-color: #f8fafc;
        }

        .email-container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .header {
          background: linear-gradient(135deg, ${brandColor} 0%, ${brandColor}dd 100%);
          padding: 40px 30px;
          text-align: center;
          color: white;
        }

        .logo {
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 12px;
          margin: 0 auto 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          font-weight: bold;
        }

        .header h1 {
          font-size: 28px;
          font-weight: 700;
          margin-bottom: 8px;
        }

        .header p {
          font-size: 16px;
          opacity: 0.9;
        }

        .content {
          padding: 40px 30px;
        }

        .greeting {
          font-size: 18px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 20px;
        }

        .message {
          font-size: 16px;
          color: #4b5563;
          margin-bottom: 30px;
          line-height: 1.7;
        }

        .reset-info {
          background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
          border: 1px solid #f59e0b;
          border-radius: 12px;
          padding: 25px;
          margin: 30px 0;
        }

        .reset-info h3 {
          color: #92400e;
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 15px;
          display: flex;
          align-items: center;
        }

        .reset-info h3::before {
          content: "🔐";
          margin-right: 8px;
        }

        .security-notice {
          background: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }

        .security-notice h4 {
          color: #dc2626;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 10px;
          display: flex;
          align-items: center;
        }

        .security-notice h4::before {
          content: "⚠️";
          margin-right: 8px;
        }

        .security-notice ul {
          color: #7f1d1d;
          font-size: 14px;
          margin-left: 20px;
        }

        .security-notice li {
          margin-bottom: 5px;
        }

        .cta-section {
          text-align: center;
          margin: 40px 0;
        }

        .cta-button {
          display: inline-block;
          background: linear-gradient(135deg, ${brandColor} 0%, ${brandColor}dd 100%);
          color: white;
          text-decoration: none;
          padding: 16px 32px;
          border-radius: 8px;
          font-weight: 600;
          font-size: 16px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          transition: transform 0.2s ease;
        }

        .cta-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .expiry-notice {
          background: #f0f9ff;
          border-radius: 8px;
          padding: 20px;
          margin: 30px 0;
          text-align: center;
        }

        .expiry-notice h4 {
          color: #0369a1;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 10px;
        }

        .expiry-notice p {
          color: #0c4a6e;
          font-size: 14px;
        }

        .footer {
          background: #f8fafc;
          padding: 30px;
          text-align: center;
          border-top: 1px solid #e5e7eb;
        }

        .footer-logo {
          font-size: 20px;
          font-weight: 700;
          color: ${brandColor};
          margin-bottom: 10px;
        }

        .footer-text {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 20px;
        }

        .footer-links {
          font-size: 12px;
          color: #9ca3af;
        }

        .footer-links a {
          color: ${brandColor};
          text-decoration: none;
          margin: 0 10px;
        }

        @media (max-width: 600px) {
          .email-container {
            margin: 0;
            border-radius: 0;
          }

          .header, .content, .footer {
            padding: 30px 20px;
          }
        }
      </style>
    </head>
    <body>
      <div class="email-container">
        <!-- Header -->
        <div class="header">
           <div class="logo"><img src="https://capacitybay.org/capalogo.png" alt="Logo" width="60"></div>
          <h1>Password Reset</h1>
          <p>Secure access to your ${userTypeDisplay.toLowerCase()} account</p>
        </div>

        <!-- Content -->
        <div class="content">
          <div class="greeting">Hello ${data.userName},</div>

          <div class="message">
            We received a request to reset the password for your CapacityBay ${userTypeDisplay} account.
            If you made this request, click the button below to reset your password.
          </div>

          <!-- Reset Information -->
          <div class="reset-info">
            <h3>Password Reset Request</h3>
            <p style="color: #92400e; margin-bottom: 20px;">
              This password reset link is valid for <strong>1 hour</strong> from the time it was generated.
              After that, you'll need to request a new reset link.
            </p>

            <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #e5e7eb;">
              <div style="font-size: 12px; font-weight: 600; color: #6b7280; text-transform: uppercase; letter-spacing: 0.5px; margin-bottom: 5px;">
                Account Type
              </div>
              <div style="font-size: 14px; font-weight: 600; color: #1f2937;">
                ${userTypeDisplay}
              </div>
            </div>
          </div>

          <!-- Call to Action -->
          <div class="cta-section">
            <a href="${data.resetUrl}" class="cta-button">
              🔑 Reset My Password
            </a>
          </div>

          <!-- Expiry Notice -->
          <div class="expiry-notice">
            <h4>⏰ Link Expires Soon</h4>
            <p>
              This reset link will expire in 1 hour for security reasons.
              If you don't reset your password within this time, you'll need to request a new link.
            </p>
          </div>

          <!-- Security Notice -->
          <div class="security-notice">
            <h4>Security Notice</h4>
            <ul>
              <li>If you didn't request this password reset, please ignore this email</li>
              <li>Never share your password reset link with anyone</li>
              <li>Always use a strong, unique password for your account</li>
              <li>Contact support if you suspect unauthorized access</li>
            </ul>
          </div>

          <div class="message">
            <strong>Need Help?</strong><br>
            If you're having trouble with the reset link, copy and paste this URL into your browser:<br>
            <span style="word-break: break-all; color: #6b7280; font-size: 14px;">${data.resetUrl}</span>
          </div>
        </div>

        <!-- Footer -->
        <div class="footer">
          <div class="footer-logo">CapacityBay</div>
          <div class="footer-text">
            Secure certification platform for the next generation of developers.
          </div>

          <div class="footer-links">
            <a href="https://certverify.capacitybay.org/privacy">Privacy Policy</a> |
            <a href="https://certverify.capacitybay.org/terms">Terms of Service</a> |
            <a href="mailto:<EMAIL>">Support</a>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}
