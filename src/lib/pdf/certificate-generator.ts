import puppeteerCore from "puppeteer-core";
import puppeteer from "puppeteer";
import chromium from "@sparticuz/chromium";
import QRCode from "qrcode";
import fs from "fs";
import path from "path";
import { ICertificate, IStudent, ICourse } from "@/lib/db/models";
import {
  generateCertificateHTML,
  CertificateData as TemplateData,
} from "./certificate-template";

interface CertificateData {
  certificate: ICertificate;
  student: IStudent;
  course: ICourse;
}

export class CertificateGenerator {
  constructor() {
    // No initialization needed for Puppeteer approach
  }

  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const { certificate, student, course } = data;

    // Format issue date
    const issueDate = new Date(certificate.issueDate).toLocaleDateString(
      "en-US",
      {
        year: "numeric",
        month: "long",
        day: "numeric",
      }
    );

    // Generate QR code for verification - use certificateId for new certs, _id for legacy
    const certificateIdentifier =
      certificate.certificateId ||
      (certificate._id as any)?.toString() ||
      certificate._id;
    const verificationUrl = `${
      process.env.NEXTAUTH_URL || "http://localhost:3000"
    }/verify/${certificateIdentifier}`;
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 200,
      margin: 1,
      color: {
        dark: "#1f2937",
        light: "#ffffff",
      },
    });

    // Convert logo to base64
    let logoBase64 = "";
    try {
      const logoPath = path.join(process.cwd(), "public", "capalogo.png");
      const logoBuffer = fs.readFileSync(logoPath);
      logoBase64 = logoBuffer.toString("base64");
    } catch (error) {
      console.warn("Could not load logo file:", error);
    }

    // Convert signature to base64
    let signatureBase64 = "";
    try {
      const signaturePath = path.join(process.cwd(), "public", "signature.png");
      const signatureBuffer = fs.readFileSync(signaturePath);
      signatureBase64 = signatureBuffer.toString("base64");
    } catch (error) {
      console.warn("Could not load signature file:", error);
    }

    // Create template data
    const templateData: TemplateData = {
      studentName: student.name,
      courseName: course.title,
      completionDate: issueDate,
      certificateId: certificateIdentifier,
      qrCodeDataUrl,
      logoBase64,
      signatureBase64,
    };

    // Create HTML template
    const html = generateCertificateHTML(templateData);

    // Configure Puppeteer for different environments
    let browser;

    if (process.env.VERCEL_ENV === "production") {
      // Use Chromium for Vercel serverless environment
      const executablePath = await chromium.executablePath();

      browser = await puppeteerCore.launch({
        args: chromium.args,
        executablePath,
        headless: true,
      });
    } else {
      // Use local Puppeteer for development
      browser = await puppeteer.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--single-process",
          "--disable-gpu",
        ],
      });
    }

    try {
      const page = await browser.newPage();
      await page.setContent(html, { waitUntil: "networkidle0" });

      const pdfBuffer = await page.pdf({
        format: "A4",
        landscape: true,
        printBackground: true,
        margin: {
          top: "0",
          right: "0",
          bottom: "0",
          left: "0",
        },
      });

      return Buffer.from(pdfBuffer);
    } finally {
      await browser.close();
    }
  }
}
