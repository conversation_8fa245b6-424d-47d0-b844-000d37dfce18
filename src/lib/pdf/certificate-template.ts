export interface CertificateData {
  studentName: string;
  courseName: string;
  completionDate: string;
  certificateId: string;
  qrCodeDataUrl?: string;
  logoBase64?: string;
  signatureBase64?: string;
}

export function generateCertificateHTML(data: CertificateData): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Certificate of Completion</title>
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');

        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Inter', sans-serif;
          width: 297mm;
          height: 210mm;
          overflow: hidden;
          position: relative;
          background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
        }

        .certificate {
          width: 100%;
          height: 100%;
          position: relative;
          overflow: hidden;
          background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }
        
        /* Modern Border Design */
        .certificate-border {
          position: absolute;
          top: 20px;
          left: 20px;
          right: 20px;
          bottom: 20px;
          border: 3px solid #10b981;
          border-radius: 16px;
          background: white;
          box-shadow: 0 20px 40px rgba(16, 185, 129, 0.1);
        }

        .certificate-border::before {
          content: '';
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          background: linear-gradient(45deg, #10b981, #059669, #047857, #10b981);
          border-radius: 16px;
          z-index: -1;
        }

        /* Decorative Corner Elements */
        .corner-decoration {
          position: absolute;
          width: 80px;
          height: 80px;
        }

        .corner-top-left {
          top: 40px;
          left: 40px;
          background: linear-gradient(135deg, #10b981, #059669);
          clip-path: polygon(0 0, 100% 0, 0 100%);
          opacity: 0.1;
        }

        .corner-top-right {
          top: 40px;
          right: 40px;
          background: linear-gradient(225deg, #10b981, #059669);
          clip-path: polygon(100% 0, 100% 100%, 0 0);
          opacity: 0.1;
        }

        .corner-bottom-left {
          bottom: 40px;
          left: 40px;
          background: linear-gradient(45deg, #10b981, #059669);
          clip-path: polygon(0 0, 100% 100%, 0 100%);
          opacity: 0.1;
        }

        .corner-bottom-right {
          bottom: 40px;
          right: 40px;
          background: linear-gradient(315deg, #10b981, #059669);
          clip-path: polygon(100% 0, 100% 100%, 0 100%);
          opacity: 0.1;
        }

        /* Subtle Pattern Overlay */
        .pattern-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image:
            radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
          pointer-events: none;
        }
        
        /* Header Section */
        .header {
          position: absolute;
          top: 50px;
          left: 60px;
          right: 60px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          z-index: 10;
        }

        .logo-section {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .logo {
          width: 60px;
          height: 60px;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
        }

        .logo img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .brand-info {
          display: flex;
          flex-direction: column;
        }

        .brand-name {
          font-size: 20px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 2px;
        }

        .brand-tagline {
          font-size: 12px;
          color: #6b7280;
          font-weight: 500;
        }

        .certificate-badge {
          background: linear-gradient(135deg, #10b981, #059669);
          color: white;
          padding: 8px 16px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 1px;
        }
        
        /* Main Content */
        .content {
          position: absolute;
          top: 140px;
          left: 60px;
          right: 60px;
          bottom: 120px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          text-align: center;
          z-index: 10;
        }

        .certificate-title {
          font-family: 'Playfair Display', serif;
          font-size: 64px;
          font-weight: 800;
          color: #1f2937;
          margin-bottom: 8px;
          letter-spacing: 2px;
        }

        .certificate-subtitle {
          font-size: 18px;
          font-weight: 500;
          color: #10b981;
          text-transform: uppercase;
          letter-spacing: 3px;
          margin-bottom: 60px;
        }

        .awarded-section {
          margin-bottom: 20px;
        }

        .awarded-text {
          font-size: 16px;
          color: #6b7280;
          font-weight: 500;
          margin-bottom: 20px;
          text-transform: uppercase;
          letter-spacing: 2px;
        }

        .recipient-name {
          font-family: 'Playfair Display', serif;
          font-size: 42px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 8px;
          position: relative;
        }

        .recipient-name::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 120px;
          height: 3px;
          background: linear-gradient(90deg, #10b981, #059669);
          border-radius: 2px;
        }
        
        /* Achievement Section */
        .achievement-section {
          margin: 20px 0;
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .achievement-text {
          font-size: 16px;
          color: #6b7280;
          margin-bottom: 20px;
          font-weight: 500;
        }

        .course-name {
          font-family: 'Playfair Display', serif;
          font-size: 32px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 10px;
          text-align: center;
        }

        /* Excellence Badge */
        .excellence-badge {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          background: linear-gradient(135deg, #10b981, #059669);
          color: white;
          padding: 12px 24px;
          border-radius: 50px;
          margin: 20px 0;
        }

        .excellence-icon {
          width: 24px;
          height: 24px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
        }

        .excellence-text {
          font-size: 14px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 1px;
        }

        .completion-date {
          font-size: 16px;
          color: #6b7280;
          font-weight: 500;
          margin-top: 20px;
        }
        
        /* Footer */
        .footer {
          position: absolute;
          bottom: 40px;
          left: 60px;
          right: 60px;
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          z-index: 10;
        }

        .qr-section {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
        }

        .qr-code {
          width: 120px;
          height: 120px;
          background: #f8fafc;
          border: 2px solid #e5e7eb;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: #6b7280;
          text-align: center;
          line-height: 1.2;
        }

        .qr-label {
          font-size: 10px;
          color: #6b7280;
          font-weight: 500;
          text-align: center;
        }

        .center-info {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 16px;
        }

        .certificate-id {
          background: linear-gradient(135deg, #f8fafc, #f1f5f9);
          border: 1px solid #e5e7eb;
          padding: 12px 20px;
          border-radius: 8px;
          text-align: center;
        }

        .id-label {
          font-size: 10px;
          color: #6b7280;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-bottom: 4px;
        }

        .id-value {
          font-family: 'Courier New', monospace;
          font-size: 12px;
          color: #1f2937;
          font-weight: 600;
        }

        .verification-url {
          font-size: 10px;
          color: #10b981;
          font-weight: 500;
        }

        .signature-section {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0px;
        }

        .signature-image {
          width: 150px;
          object-fit: contain;
          margin-bottom: 2px;
        }

        .signature-line {
          width: 160px;
          height: 2px;
          background: #d1d5db;
        }

        .signature-label {
          font-size: 10px;
          color: #6b7280;
          font-weight: 500;
          text-align: center;
        }

        .authority-name {
          font-size: 12px;
          color: #1f2937;
          font-weight: 600;
          margin-top: 4px;
        }
      </style>
    </head>
    <body>
      <div class="certificate">
        <!-- Certificate Border -->
        <div class="certificate-border"></div>

        <!-- Pattern Overlay -->
        <div class="pattern-overlay"></div>

        <!-- Corner Decorations -->
        <div class="corner-decoration corner-top-left"></div>
        <div class="corner-decoration corner-top-right"></div>
        <div class="corner-decoration corner-bottom-left"></div>
        <div class="corner-decoration corner-bottom-right"></div>

        <!-- Header -->
        <div class="header">
          <div class="logo-section">
            <div class="logo">
              <img src="data:image/png;base64,${data.logoBase64 || ''}" alt="CapacityBay Logo" />
            </div>
            <div class="brand-info">
              <div class="brand-name">CapacityBay</div>
              <div class="brand-tagline">Excellence in Education</div>
            </div>
          </div>
          <div class="certificate-badge">Verified Certificate</div>
        </div>

        <!-- Main Content -->
        <div class="content">
          <h1 class="certificate-title">Certificate</h1>
          <p class="certificate-subtitle">of Completion</p>

          <div class="awarded-section">
            <p class="awarded-text">This is to certify that</p>
            <h2 class="recipient-name">${data.studentName}</h2>
          </div>

          <div class="achievement-section">
            <p class="achievement-text">has successfully completed the course</p>
            <h3 class="course-name">${data.courseName}</h3>
            
            <p class="completion-date">Completed on ${data.completionDate}</p>
            <div class="excellence-badge">
              <div class="excellence-icon">★</div>
              <div class="excellence-text">Certificate of Excellence</div>
            </div>

          </div>
        </div>

        <!-- Footer -->
        <div class="footer">
          <div class="qr-section">
            ${data.qrCodeDataUrl ?
              `<img src="${data.qrCodeDataUrl}" alt="QR Code" class="qr-code" style="width: 120px; height: 120px; border-radius: 8px;">` :
              `<div class="qr-code">QR CODE<br><small>Scan to Verify</small></div>`
            }
            <div class="qr-label">Digital Verification</div>
          </div>

          <div class="center-info">
            <div class="certificate-id">
              <div class="id-label">Certificate ID</div>
              <div class="id-value">${data.certificateId}</div>
            </div>
            <div class="verification-url">https://certverify.capacitybay.org/verify/${data.certificateId}</div>
          </div>

          <div class="signature-section">
            ${data.signatureBase64 ?
              `<img src="data:image/png;base64,${data.signatureBase64}" alt="Signature" class="signature-image" />` :
              `<div class="signature-line"></div>`
            }
            <div class="signature-label">Authorized Signature</div>
            <div class="authority-name">Obinna Nwachukwu</div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
}
