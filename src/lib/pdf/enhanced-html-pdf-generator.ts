import { jsPDF } from 'jspdf';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { ICertificate, IStudent, ICourse } from '@/lib/db/models';

interface CertificateData {
  certificate: ICertificate;
  student: IStudent;
  course: ICourse;
}

export class EnhancedHTMLPDFGenerator {
  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const { certificate, student, course } = data;

    // Format issue date to match your template
    const issueDate = new Date(certificate.issueDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Generate QR code for verification
    const certificateIdentifier = certificate.certificateId || (certificate._id as any)?.toString() || certificate._id;
    const verificationUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/verify/${certificateIdentifier}`;

    // Generate QR code as data URL
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 120,
      margin: 1,
      color: {
        dark: '#1f2937',
        light: '#ffffff'
      }
    });

    // Load logo as base64
    let logoBase64 = '';
    try {
      const logoPath = path.join(process.cwd(), 'public', 'capalogo.png');
      const logoBuffer = fs.readFileSync(logoPath);
      logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
    } catch (error) {
      console.warn('Could not load logo file:', error);
    }

    // Create PDF with A4 landscape orientation to match your HTML template exactly
    const doc = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = doc.internal.pageSize.getWidth(); // 297mm
    const pageHeight = doc.internal.pageSize.getHeight(); // 210mm

    // Recreate your HTML template's gradient background
    // Create gradient effect with multiple overlapping rectangles
    doc.setFillColor(248, 250, 252); // #f8fafc
    doc.rect(0, 0, pageWidth, pageHeight, 'F');
    
    // Add gradient layers to simulate your CSS gradient
    doc.setFillColor(255, 255, 255, 0.9);
    doc.rect(0, 0, pageWidth * 0.6, pageHeight, 'F');
    
    doc.setFillColor(241, 245, 249, 0.7); // #f1f5f9
    doc.rect(pageWidth * 0.4, 0, pageWidth * 0.6, pageHeight, 'F');

    // Modern border design matching your HTML template
    doc.setDrawColor(16, 185, 129); // #10b981 - your green color
    doc.setLineWidth(2);
    
    // Outer border with rounded corners
    doc.roundedRect(15, 15, pageWidth - 30, pageHeight - 30, 8, 8, 'S');
    
    // Inner decorative border
    doc.setDrawColor(229, 231, 235); // #e5e7eb - light gray
    doc.setLineWidth(1);
    doc.roundedRect(25, 25, pageWidth - 50, pageHeight - 50, 5, 5, 'S');

    // Add corner decorative elements (simplified geometric shapes)
    doc.setFillColor(16, 185, 129, 0.1); // Semi-transparent green
    
    // Top corners
    doc.circle(40, 40, 8, 'F');
    doc.circle(pageWidth - 40, 40, 8, 'F');
    
    // Bottom corners  
    doc.circle(40, pageHeight - 40, 8, 'F');
    doc.circle(pageWidth - 40, pageHeight - 40, 8, 'F');

    // Header Section - Logo and Brand (matching your template layout)
    if (logoBase64) {
      try {
        doc.addImage(logoBase64, 'PNG', 50, 45, 25, 25);
      } catch (error) {
        console.warn('Could not add logo to PDF:', error);
      }
    }

    // Brand text (right side of logo)
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(16);
    doc.setTextColor(31, 41, 55); // #1f2937 - dark gray
    doc.text('CAPACITY BAY', 85, 55);
    
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    doc.setTextColor(107, 114, 128); // #6b7280 - medium gray
    doc.text('PROFESSIONAL DEVELOPMENT', 85, 62);

    // Certificate Title (centered, large, elegant)
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(32);
    doc.setTextColor(31, 41, 55); // #1f2937
    doc.text('CERTIFICATE', pageWidth / 2, 95, { align: 'center' });
    
    doc.setFontSize(24);
    doc.setTextColor(107, 114, 128); // #6b7280
    doc.text('OF COMPLETION', pageWidth / 2, 105, { align: 'center' });

    // Decorative line under title
    doc.setDrawColor(16, 185, 129); // #10b981
    doc.setLineWidth(2);
    doc.line(pageWidth / 2 - 40, 110, pageWidth / 2 + 40, 110);

    // Main content section
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(14);
    doc.setTextColor(107, 114, 128); // #6b7280
    doc.text('This is to certify that', pageWidth / 2, 125, { align: 'center' });

    // Student name (large, prominent)
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(28);
    doc.setTextColor(31, 41, 55); // #1f2937
    doc.text(student.name.toUpperCase(), pageWidth / 2, 140, { align: 'center' });

    // Course completion text
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(14);
    doc.setTextColor(107, 114, 128); // #6b7280
    doc.text('has successfully completed the course', pageWidth / 2, 155, { align: 'center' });

    // Course name (prominent)
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(20);
    doc.setTextColor(16, 185, 129); // #10b981 - green accent
    doc.text(course.title, pageWidth / 2, 170, { align: 'center' });

    // Issue date
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(12);
    doc.setTextColor(107, 114, 128); // #6b7280
    doc.text(`Issued on ${issueDate}`, pageWidth / 2, 185, { align: 'center' });

    // QR Code (bottom left)
    if (qrCodeDataUrl) {
      try {
        doc.addImage(qrCodeDataUrl, 'PNG', 50, pageHeight - 80, 25, 25);
        
        doc.setFontSize(8);
        doc.setTextColor(107, 114, 128);
        doc.text('Scan to verify', 62.5, pageHeight - 50, { align: 'center' });
      } catch (error) {
        console.warn('Could not add QR code to PDF:', error);
      }
    }

    // Certificate ID (bottom center)
    doc.setFontSize(10);
    doc.setTextColor(107, 114, 128);
    doc.text(`Certificate ID: ${certificateIdentifier}`, pageWidth / 2, pageHeight - 35, { align: 'center' });

    // Verification URL
    doc.setFontSize(8);
    doc.setTextColor(16, 185, 129);
    doc.text(`Verify at: ${process.env.NEXTAUTH_URL || 'https://certverify.capacitybay.org'}/verify/${certificateIdentifier}`, 
              pageWidth / 2, pageHeight - 25, { align: 'center' });

    // Signature section (bottom right)
    doc.setDrawColor(209, 213, 219); // Light gray line
    doc.setLineWidth(1);
    doc.line(pageWidth - 120, pageHeight - 65, pageWidth - 50, pageHeight - 65);

    doc.setFontSize(8);
    doc.setTextColor(107, 114, 128);
    doc.text('Authorized Signature', pageWidth - 85, pageHeight - 58, { align: 'center' });

    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(31, 41, 55);
    doc.text('Obinna Nwachukwu', pageWidth - 85, pageHeight - 45, { align: 'center' });
    
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(9);
    doc.setTextColor(107, 114, 128);
    doc.text('Director', pageWidth - 85, pageHeight - 38, { align: 'center' });

    // Convert to buffer
    const pdfBuffer = Buffer.from(doc.output('arraybuffer'));
    return pdfBuffer;
  }
}
