import { jsPDF } from 'jspdf';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { ICertificate, IStudent, ICourse } from '@/lib/db/models';
import { generateCertificateHTML } from './certificate-template';
import html2canvas from 'html2canvas';
import { JSDOM } from 'jsdom';

interface CertificateData {
  certificate: ICertificate;
  student: IStudent;
  course: ICourse;
}

export class HTMLTemplatePDFGenerator {
  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const { certificate, student, course } = data;

    // Format issue date to match your template
    const issueDate = new Date(certificate.issueDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Generate QR code for verification
    const certificateIdentifier = certificate.certificateId || (certificate._id as any)?.toString() || certificate._id;
    const verificationUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/verify/${certificateIdentifier}`;

    // Generate QR code as data URL
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 150,
      margin: 1,
      color: {
        dark: '#1f2937',
        light: '#ffffff'
      }
    });

    // Load logo as base64
    let logoBase64 = '';
    try {
      const logoPath = path.join(process.cwd(), 'public', 'capalogo.png');
      const logoBuffer = fs.readFileSync(logoPath);
      logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
    } catch (error) {
      console.warn('Could not load logo file:', error);
    }

    // Prepare template data
    const templateData = {
      studentName: student.name,
      courseName: course.title,
      completionDate: issueDate,
      certificateId: certificateIdentifier,
      qrCodeDataUrl,
      logoBase64
    };

    // Generate HTML using your existing template
    const html = generateCertificateHTML(templateData);

    // Create a virtual DOM environment for server-side rendering
    const dom = new JSDOM(html, {
      resources: 'usable',
      runScripts: 'dangerously',
      pretendToBeVisual: true,
      includeNodeLocations: true
    });

    const window = dom.window;
    const document = window.document;

    // Set up html2canvas in the virtual environment
    (global as any).window = window;
    (global as any).document = document;
    (global as any).HTMLCanvasElement = window.HTMLCanvasElement;
    (global as any).Image = window.Image;

    try {
      // Wait for fonts to load (simulate)
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Get the certificate element
      const certificateElement = document.querySelector('.certificate') as HTMLElement;
      
      if (!certificateElement) {
        throw new Error('Certificate element not found in template');
      }

      // Use html2canvas to render the HTML to canvas
      const canvas = await html2canvas(certificateElement, {
        width: 1123, // A4 landscape width in pixels at 96 DPI
        height: 794,  // A4 landscape height in pixels at 96 DPI
        scale: 2,     // High DPI for better quality
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff'
      });

      // Convert canvas to image data
      const imgData = canvas.toDataURL('image/png');

      // Create PDF with jsPDF
      const doc = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      // Add the rendered HTML as an image to the PDF
      const pageWidth = doc.internal.pageSize.getWidth();
      const pageHeight = doc.internal.pageSize.getHeight();

      doc.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);

      // Convert to buffer
      const pdfBuffer = Buffer.from(doc.output('arraybuffer'));
      return pdfBuffer;

    } finally {
      // Clean up global variables
      delete (global as any).window;
      delete (global as any).document;
      delete (global as any).HTMLCanvasElement;
      delete (global as any).Image;
      
      // Close the JSDOM window
      dom.window.close();
    }
  }
}
