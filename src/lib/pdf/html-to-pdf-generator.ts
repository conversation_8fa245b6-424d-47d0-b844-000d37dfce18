import puppeteerCore from 'puppeteer-core';
import puppeteer from 'puppeteer';
import chromium from '@sparticuz/chromium';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { ICertificate, IStudent, ICourse } from '@/lib/db/models';
import { generateCertificateHTML } from './certificate-template';

interface CertificateData {
  certificate: ICertificate;
  student: IStudent;
  course: ICourse;
}

export class HTMLToPDFGenerator {
  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const { certificate, student, course } = data;

    // Format issue date
    const issueDate = new Date(certificate.issueDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Generate QR code for verification
    const certificateIdentifier = certificate.certificateId || (certificate._id as any)?.toString() || certificate._id;
    const verificationUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/verify/${certificateIdentifier}`;
    
    // Generate QR code as data URL
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 150,
      margin: 1,
      color: {
        dark: '#1f2937',
        light: '#ffffff'
      }
    });

    // Load logo as base64
    let logoBase64 = '';
    try {
      const logoPath = path.join(process.cwd(), 'public', 'capalogo.png');
      const logoBuffer = fs.readFileSync(logoPath);
      logoBase64 = logoBuffer.toString('base64');
    } catch (error) {
      console.warn('Could not load logo file:', error);
    }

    // Load signature as base64 (optional)
    let signatureBase64 = '';
    try {
      const signaturePath = path.join(process.cwd(), 'public', 'signature.png');
      const signatureBuffer = fs.readFileSync(signaturePath);
      signatureBase64 = signatureBuffer.toString('base64');
    } catch (error) {
      console.warn('Could not load signature file:', error);
    }

    // Prepare data for your HTML template
    const templateData = {
      studentName: student.name,
      courseName: course.title,
      completionDate: issueDate,
      certificateId: certificateIdentifier,
      qrCodeDataUrl: qrCodeDataUrl,
      logoBase64: logoBase64,
      signatureBase64: signatureBase64
    };

    // Generate HTML using your existing template
    const html = generateCertificateHTML(templateData);

    // Configure Puppeteer for different environments
    const isProduction = process.env.NODE_ENV === 'production';
    const isVercel = process.env.VERCEL === '1';

    let browser;

    if (isVercel) {
      // Use Chromium for Vercel serverless environment
      browser = await puppeteerCore.launch({
        args: chromium.args,
        defaultViewport: {
          width: 1280,
          height: 720
        },
        executablePath: await chromium.executablePath(),
        headless: true,
      });
    } else if (isProduction) {
      // Use regular Puppeteer for other production environments
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      });
    } else {
      // Use local Puppeteer for development (includes Chrome)
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu'
        ]
      });
    }

    try {
      const page = await browser.newPage();
      
      // Set viewport for A4 landscape
      await page.setViewport({
        width: 1123, // A4 landscape width in pixels at 96 DPI
        height: 794,  // A4 landscape height in pixels at 96 DPI
        deviceScaleFactor: 2 // High DPI for better quality
      });

      // Set content and wait for fonts to load
      await page.setContent(html, {
        waitUntil: ['networkidle0', 'domcontentloaded']
      });

      // Wait a bit more for Google Fonts to load
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate PDF with your template's exact dimensions
      const pdfBuffer = await page.pdf({
        format: 'A4',
        landscape: true,
        printBackground: true,
        preferCSSPageSize: true,
        margin: {
          top: '0mm',
          right: '0mm',
          bottom: '0mm',
          left: '0mm'
        }
      });

      return Buffer.from(pdfBuffer);

    } finally {
      await browser.close();
    }
  }
}
