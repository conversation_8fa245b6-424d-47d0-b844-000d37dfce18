import { jsPDF } from 'jspdf';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { ICertificate, IStudent, ICourse } from '@/lib/db/models';

interface CertificateData {
  certificate: ICertificate;
  student: IStudent;
  course: ICourse;
}

export class JSPDFCertificateGenerator {
  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const { certificate, student, course } = data;

    // Format issue date to match your template
    const issueDate = new Date(certificate.issueDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Generate QR code for verification
    const certificateIdentifier = certificate.certificateId || (certificate._id as any)?.toString() || certificate._id;
    const verificationUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/verify/${certificateIdentifier}`;

    // Generate QR code as data URL
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 120,
      margin: 1,
      color: {
        dark: '#1f2937',
        light: '#ffffff'
      }
    });

    // Load logo as base64
    let logoBase64 = '';
    try {
      const logoPath = path.join(process.cwd(), 'public', 'capalogo.png');
      const logoBuffer = fs.readFileSync(logoPath);
      logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
    } catch (error) {
      console.warn('Could not load logo file:', error);
    }

    // Create PDF document (A4 landscape) - matching your template dimensions
    const doc = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });

    const pageWidth = doc.internal.pageSize.getWidth(); // 297mm
    const pageHeight = doc.internal.pageSize.getHeight(); // 210mm

    // Set background gradient effect (simulate with white background)
    doc.setFillColor(255, 255, 255);
    doc.rect(0, 0, pageWidth, pageHeight, 'F');

    // Modern border design - matching your template
    doc.setDrawColor(16, 185, 129); // #10b981 green
    doc.setLineWidth(3);
    doc.roundedRect(20, 20, pageWidth - 40, pageHeight - 40, 5, 5, 'S');

    // Add subtle corner decorations (simplified for jsPDF)
    doc.setFillColor(16, 185, 129);

    // Corner decoration rectangles (simplified version)
    doc.rect(40, 40, 20, 20, 'F');
    doc.rect(pageWidth - 60, 40, 20, 20, 'F');
    doc.rect(40, pageHeight - 60, 20, 20, 'F');
    doc.rect(pageWidth - 60, pageHeight - 60, 20, 20, 'F');

    // Header Section - Logo and Brand Info (left side)
    if (logoBase64) {
      try {
        doc.addImage(logoBase64, 'PNG', 60, 50, 20, 20); // Rounded logo effect
      } catch (error) {
        console.warn('Could not add logo to PDF:', error);
      }
    }

    // Brand name and tagline
    doc.setFontSize(20);
    doc.setTextColor(31, 41, 55); // #1f2937
    doc.text('CapacityBay', 85, 58);

    doc.setFontSize(12);
    doc.setTextColor(107, 114, 128); // #6b7280
    doc.text('Excellence in Education', 85, 65);

    // Verified Certificate Badge (right side)
    doc.setFillColor(16, 185, 129); // Green gradient effect
    doc.roundedRect(pageWidth - 120, 50, 60, 20, 10, 10, 'F');

    doc.setFontSize(10);
    doc.setTextColor(255, 255, 255); // White text
    doc.text('VERIFIED CERTIFICATE', pageWidth - 90, 62, { align: 'center' });

    // Main Content Section - Certificate Title (large and elegant)
    doc.setFontSize(48); // Large title like your template
    doc.setTextColor(31, 41, 55); // #1f2937
    doc.text('Certificate', pageWidth / 2, 100, { align: 'center' });

    // Subtitle
    doc.setFontSize(16);
    doc.setTextColor(16, 185, 129); // #10b981 green
    doc.text('OF COMPLETION', pageWidth / 2, 115, { align: 'center' });

    // "This is to certify that" text
    doc.setFontSize(14);
    doc.setTextColor(107, 114, 128); // #6b7280
    doc.text('THIS IS TO CERTIFY THAT', pageWidth / 2, 135, { align: 'center' });

    // Student name (large and prominent)
    doc.setFontSize(32); // Large name like your template
    doc.setTextColor(31, 41, 55); // #1f2937
    doc.text(student.name.toUpperCase(), pageWidth / 2, 155, { align: 'center' });

    // Decorative line under name
    doc.setDrawColor(16, 185, 129);
    doc.setLineWidth(2);
    doc.line(pageWidth / 2 - 40, 160, pageWidth / 2 + 40, 160);

    // Achievement text
    doc.setFontSize(14);
    doc.setTextColor(107, 114, 128);
    doc.text('has successfully completed the course', pageWidth / 2, 175, { align: 'center' });

    // Course name (prominent)
    doc.setFontSize(24);
    doc.setTextColor(31, 41, 55);
    doc.text(course.title, pageWidth / 2, 195, { align: 'center' });

    // Excellence Badge
    doc.setFillColor(16, 185, 129);
    doc.roundedRect(pageWidth / 2 - 40, 205, 80, 15, 7, 7, 'F');

    doc.setFontSize(10);
    doc.setTextColor(255, 255, 255);
    doc.text('★ CERTIFICATE OF EXCELLENCE', pageWidth / 2, 215, { align: 'center' });

    // Completion date
    doc.setFontSize(14);
    doc.setTextColor(107, 114, 128);
    doc.text(`Completed on ${issueDate}`, pageWidth / 2, 235, { align: 'center' });

    // Footer Section - QR Code (left)
    if (qrCodeDataUrl) {
      try {
        doc.addImage(qrCodeDataUrl, 'PNG', 60, pageHeight - 80, 30, 30);
      } catch (error) {
        console.warn('Could not add QR code to PDF:', error);
      }
    }

    // QR code label
    doc.setFontSize(8);
    doc.setTextColor(107, 114, 128);
    doc.text('Digital Verification', 75, pageHeight - 45, { align: 'center' });

    // Center Info - Certificate ID
    doc.setFillColor(248, 250, 252); // Light gray background
    doc.setDrawColor(229, 231, 235); // Border
    doc.roundedRect(pageWidth / 2 - 40, pageHeight - 75, 80, 25, 3, 3, 'FD');

    doc.setFontSize(8);
    doc.setTextColor(107, 114, 128);
    doc.text('CERTIFICATE ID', pageWidth / 2, pageHeight - 68, { align: 'center' });

    doc.setFontSize(10);
    doc.setTextColor(31, 41, 55);
    doc.text(certificateIdentifier, pageWidth / 2, pageHeight - 58, { align: 'center' });

    // Verification URL
    doc.setFontSize(8);
    doc.setTextColor(16, 185, 129);
    doc.text(`https://certverify.capacitybay.org/verify/${certificateIdentifier}`, pageWidth / 2, pageHeight - 45, { align: 'center' });

    // Signature Section (right)
    doc.setDrawColor(209, 213, 219); // Light gray line
    doc.setLineWidth(1);
    doc.line(pageWidth - 120, pageHeight - 55, pageWidth - 40, pageHeight - 55);

    doc.setFontSize(8);
    doc.setTextColor(107, 114, 128);
    doc.text('Authorized Signature', pageWidth - 80, pageHeight - 48, { align: 'center' });

    doc.setFontSize(10);
    doc.setTextColor(31, 41, 55);
    doc.text('Obinna Nwachukwu', pageWidth - 80, pageHeight - 40, { align: 'center' });

    // Convert to buffer
    const pdfBuffer = Buffer.from(doc.output('arraybuffer'));
    return pdfBuffer;
  }
}
