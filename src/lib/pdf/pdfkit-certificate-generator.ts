import PDFDocument from 'pdfkit';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { ICertificate, IStudent, ICourse } from '@/lib/db/models';

interface CertificateData {
  certificate: ICertificate;
  student: IStudent;
  course: ICourse;
}

export class PDFKitCertificateGenerator {
  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const { certificate, student, course } = data;

    // Format issue date
    const issueDate = new Date(certificate.issueDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Generate QR code for verification
    const certificateIdentifier = certificate.certificateId || (certificate._id as any)?.toString() || certificate._id;
    const verificationUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/verify/${certificateIdentifier}`;
    
    // Generate QR code as buffer
    const qrCodeBuffer = await QRCode.toBuffer(verificationUrl, {
      width: 150,
      margin: 1,
      color: {
        dark: '#1f2937',
        light: '#ffffff'
      }
    });

    // Load logo and signature
    let logoBuffer: Buffer | null = null;
    let signatureBuffer: Buffer | null = null;

    try {
      const logoPath = path.join(process.cwd(), 'public', 'capalogo.png');
      logoBuffer = fs.readFileSync(logoPath);
    } catch (error) {
      console.warn('Could not load logo file:', error);
    }

    try {
      const signaturePath = path.join(process.cwd(), 'public', 'signature.png');
      signatureBuffer = fs.readFileSync(signaturePath);
    } catch (error) {
      console.warn('Could not load signature file:', error);
    }

    // Create PDF document with explicit font settings to avoid Helvetica
    const doc = new PDFDocument({
      size: 'A4',
      layout: 'landscape',
      margins: { top: 40, bottom: 40, left: 40, right: 40 },
      font: 'Times-Roman'  // Explicitly set default font
    });

    // Create buffer to store PDF
    const chunks: Buffer[] = [];
    doc.on('data', (chunk) => chunks.push(chunk));

    return new Promise((resolve, reject) => {
      doc.on('end', () => {
        resolve(Buffer.concat(chunks));
      });

      doc.on('error', reject);

      try {
        // Set background color
        doc.rect(0, 0, doc.page.width, doc.page.height)
           .fill('#ffffff');

        // Add border
        doc.rect(20, 20, doc.page.width - 40, doc.page.height - 40)
           .strokeColor('#22c55e')
           .lineWidth(3)
           .stroke();

        // Add decorative elements
        doc.rect(30, 30, doc.page.width - 60, 8)
           .fill('#22c55e');

        doc.rect(30, doc.page.height - 38, doc.page.width - 60, 8)
           .fill('#22c55e');

        // Add logo
        if (logoBuffer) {
          doc.image(logoBuffer, 60, 60, { width: 80, height: 80 });
        }

        // Add company name (using only standard PDF fonts)
        doc.fontSize(24)
           .fillColor('#1f2937')
           .text('CapacityBay', 160, 80, { align: 'left' });

        doc.fontSize(14)
           .fillColor('#6b7280')
           .text('Professional Development & Training', 160, 110, { align: 'left' });

        // Certificate title
        doc.fontSize(36)
           .fillColor('#22c55e')
           .text('CERTIFICATE OF COMPLETION', 0, 180, {
             align: 'center',
             width: doc.page.width
           });

        // Decorative line
        doc.moveTo(200, 240)
           .lineTo(doc.page.width - 200, 240)
           .strokeColor('#22c55e')
           .lineWidth(2)
           .stroke();

        // Certificate text
        doc.fontSize(18)
           .fillColor('#1f2937')
           .text('This is to certify that', 0, 280, {
             align: 'center',
             width: doc.page.width
           });

        // Student name
        doc.fontSize(28)
           .fillColor('#1f2937')
           .text(student.name.toUpperCase(), 0, 320, {
             align: 'center',
             width: doc.page.width
           });

        // Course completion text
        doc.fontSize(18)
           .fillColor('#1f2937')
           .text('has successfully completed the course', 0, 370, {
             align: 'center',
             width: doc.page.width
           });

        // Course name
        doc.fontSize(24)
           .fillColor('#22c55e')
           .text(course.title, 0, 410, {
             align: 'center',
             width: doc.page.width
           });

        // Issue date
        doc.fontSize(16)
           .fillColor('#6b7280')
           .text(`Issued on ${issueDate}`, 0, 460, {
             align: 'center',
             width: doc.page.width
           });

        // Certificate ID
        doc.fontSize(12)
           .fillColor('#9ca3af')
           .text(`Certificate ID: ${certificateIdentifier}`, 0, 485, {
             align: 'center',
             width: doc.page.width
           });

        // Add signature
        if (signatureBuffer) {
          doc.image(signatureBuffer, doc.page.width - 250, doc.page.height - 150, { 
            width: 120, 
            height: 60 
          });
        }

        // Signature line and text
        doc.moveTo(doc.page.width - 280, doc.page.height - 80)
           .lineTo(doc.page.width - 80, doc.page.height - 80)
           .strokeColor('#6b7280')
           .lineWidth(1)
           .stroke();

        doc.fontSize(12)
           .fillColor('#6b7280')
           .text('Authorized Signature', doc.page.width - 280, doc.page.height - 65, {
             width: 200,
             align: 'center'
           });

        // Add QR code
        if (qrCodeBuffer) {
          doc.image(qrCodeBuffer, 60, doc.page.height - 180, {
            width: 120,
            height: 120
          });
        }

        // QR code text
        doc.fontSize(10)
           .fillColor('#6b7280')
           .text('Scan to verify', 60, doc.page.height - 50, {
             width: 120,
             align: 'center'
           });

        // Verification URL
        doc.fontSize(8)
           .fillColor('#9ca3af')
           .text(verificationUrl, 60, doc.page.height - 35, {
             width: 120,
             align: 'center'
           });

        // Finalize the PDF
        doc.end();

      } catch (error) {
        reject(error);
      }
    });
  }
}
