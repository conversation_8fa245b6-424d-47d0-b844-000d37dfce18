import { chromium } from 'playwright-core';
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { ICertificate, IStudent, ICourse } from '@/lib/db/models';
import { generateCertificateHTML } from './certificate-template';

interface CertificateData {
  certificate: ICertificate;
  student: IStudent;
  course: ICourse;
}

export class PlaywrightHTMLPDFGenerator {
  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const { certificate, student, course } = data;

    // Format issue date
    const issueDate = new Date(certificate.issueDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Generate QR code for verification
    const certificateIdentifier = certificate.certificateId || (certificate._id as any)?.toString() || certificate._id;
    const verificationUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/verify/${certificateIdentifier}`;
    
    // Generate QR code as data URL
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 150,
      margin: 1,
      color: {
        dark: '#1f2937',
        light: '#ffffff'
      }
    });

    // Load logo as base64
    let logoBase64 = '';
    try {
      const logoPath = path.join(process.cwd(), 'public', 'capalogo.png');
      const logoBuffer = fs.readFileSync(logoPath);
      logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
    } catch (error) {
      console.warn('Could not load logo file:', error);
    }

    // Prepare template data exactly as your HTML template expects
    const templateData = {
      studentName: student.name,
      courseName: course.title,
      completionDate: issueDate,
      certificateId: certificateIdentifier,
      qrCodeDataUrl,
      logoBase64
    };

    // Generate HTML using your existing beautiful template
    const html = generateCertificateHTML(templateData);

    let browser;
    
    try {
      // Launch browser with Playwright (more reliable than Puppeteer on Vercel)
      if (process.env.VERCEL_ENV === "production") {
        // Use Playwright's built-in Chromium for Vercel
        browser = await chromium.launch({
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--single-process',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
          ],
          headless: true,
        });
      } else {
        // Local development
        browser = await chromium.launch({
          headless: true,
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox'
          ]
        });
      }

      const page = await browser.newPage();
      
      // Set viewport for A4 landscape to match your template
      await page.setViewportSize({
        width: 1123, // A4 landscape width in pixels at 96 DPI
        height: 794,  // A4 landscape height in pixels at 96 DPI
      });

      // Set content and wait for fonts to load (your Google Fonts)
      await page.setContent(html, {
        waitUntil: 'networkidle'
      });

      // Wait for Google Fonts to load properly
      await page.waitForTimeout(3000);

      // Generate PDF with your template's exact specifications
      const pdfBuffer = await page.pdf({
        format: 'A4',
        landscape: true,
        printBackground: true,
        preferCSSPageSize: true,
        margin: {
          top: '0mm',
          right: '0mm',
          bottom: '0mm',
          left: '0mm'
        }
      });

      return Buffer.from(pdfBuffer);

    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
}
