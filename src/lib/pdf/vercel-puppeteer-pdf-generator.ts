import chromium from "@sparticuz/chromium-min";
import puppeteerCore from "puppeteer-core";
import QRCode from 'qrcode';
import fs from 'fs';
import path from 'path';
import { ICertificate, IStudent, ICourse } from '@/lib/db/models';
import { generateCertificateHTML } from './certificate-template';

interface CertificateData {
  certificate: ICertificate;
  student: IStudent;
  course: ICourse;
}

async function getBrowser() {
  const REMOTE_PATH = process.env.CHROMIUM_REMOTE_EXEC_PATH;
  const LOCAL_PATH = process.env.CHROMIUM_LOCAL_EXEC_PATH;
  
  if (!REMOTE_PATH && !LOCAL_PATH) {
    throw new Error("Missing a path for chromium executable");
  }

  if (!!REMOTE_PATH) {
    return await puppeteerCore.launch({
      args: chromium.args,
      executablePath: await chromium.executablePath(
        process.env.CHROMIUM_REMOTE_EXEC_PATH,
      ),
      defaultViewport: null,
      headless: true,
    });
  }

  return await puppeteerCore.launch({
    executablePath: LOCAL_PATH,
    defaultViewport: null,
    headless: true,
  });
}

export class VercelPuppeteerPDFGenerator {
  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const { certificate, student, course } = data;

    // Format issue date
    const issueDate = new Date(certificate.issueDate).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Generate QR code for verification
    const certificateIdentifier = certificate.certificateId || (certificate._id as any)?.toString() || certificate._id;
    const verificationUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/verify/${certificateIdentifier}`;
    
    // Generate QR code as data URL
    const qrCodeDataUrl = await QRCode.toDataURL(verificationUrl, {
      width: 150,
      margin: 1,
      color: {
        dark: '#1f2937',
        light: '#ffffff'
      }
    });

    // Load logo as base64
    let logoBase64 = '';
    try {
      const logoPath = path.join(process.cwd(), 'public', 'capalogo.png');
      const logoBuffer = fs.readFileSync(logoPath);
      logoBase64 = `data:image/png;base64,${logoBuffer.toString('base64')}`;
    } catch (error) {
      console.warn('Could not load logo file:', error);
    }

    // Prepare template data exactly as your HTML template expects
    const templateData = {
      studentName: student.name,
      courseName: course.title,
      completionDate: issueDate,
      certificateId: certificateIdentifier,
      qrCodeDataUrl,
      logoBase64
    };

    // Generate HTML using your existing beautiful template
    const html = generateCertificateHTML(templateData);

    try {
      const browser = await getBrowser();
      const page = await browser.newPage();

      // Handle page errors
      page.on("pageerror", (err: Error) => {
        console.error("Page error:", err);
        throw err;
      });
      
      page.on("error", (err: Error) => {
        console.error("Browser error:", err);
        throw err;
      });

      // Set viewport for A4 landscape to match your template
      await page.setViewport({
        width: 1123, // A4 landscape width in pixels at 96 DPI
        height: 794,  // A4 landscape height in pixels at 96 DPI
      });

      // Set content and wait for fonts to load (your Google Fonts)
      await page.setContent(html, {
        waitUntil: ['networkidle0', 'domcontentloaded']
      });

      // Wait for Google Fonts to load properly
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Generate PDF with your template's exact specifications
      const pdf = await page.pdf({
        format: "A4",
        landscape: true,
        printBackground: true,
        margin: { top: "0mm", right: "0mm", bottom: "0mm", left: "0mm" },
        preferCSSPageSize: true,
        displayHeaderFooter: false,
        scale: 1.0,
      });

      await browser.close();
      return Buffer.from(pdf);

    } catch (error) {
      console.error("Error generating certificate PDF:", error);
      throw error;
    }
  }
}
