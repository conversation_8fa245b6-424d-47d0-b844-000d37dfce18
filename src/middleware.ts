import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;

    // Allow access to public routes
    const publicRoutes = ['/', '/verify', '/api/verify', '/api/auth'];
    const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));
    
    if (isPublicRoute) {
      return NextResponse.next();
    }

    // Redirect unauthenticated users to appropriate login page
    if (!token) {
      if (pathname.startsWith('/admin')) {
        return NextResponse.redirect(new URL('/admin/login', req.url));
      } else if (pathname.startsWith('/student')) {
        return NextResponse.redirect(new URL('/login', req.url));
      }
    }

    // Role-based access control
    if (token) {
      // Admin routes
      if (pathname.startsWith('/admin') && token.role !== 'admin') {
        return NextResponse.redirect(new URL('/admin/login', req.url));
      }
      
      // Student routes
      if (pathname.startsWith('/student') && token.role !== 'student') {
        return NextResponse.redirect(new URL('/login', req.url));
      }
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        
        // Allow access to public routes without authentication
        const publicRoutes = ['/', '/verify', '/api/verify', '/api/auth', '/login', '/admin/login'];
        const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));
        
        if (isPublicRoute) {
          return true;
        }

        // Require authentication for protected routes
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - img/ (public images)
     */
    '/((?!_next/static|_next/image|favicon.ico|img/).*)',
  ],
};
