import bcrypt from 'bcryptjs';
import { randomUUID } from 'crypto';
import dbConnect from '../lib/db/connection';
import { Admin, Student, Course, Certificate } from '../lib/db/models';

async function createTestAccounts() {
  try {
    // Connect to the database
    await dbConnect();
    console.log('Connected to database');

    // Create admin account
    const adminPassword = 'admin123';
    const hashedAdminPassword = await bcrypt.hash(adminPassword, 10);

    const adminExists = await Admin.findOne({ email: '<EMAIL>' });

    if (!adminExists) {
      await Admin.create({
        name: 'CapacityBay Admin',
        username: 'admin',
        email: '<EMAIL>',
        password: hashedAdminPassword,
        role: 'super_admin',
        permissions: ['manage_students', 'manage_courses', 'manage_certificates', 'view_analytics'],
        isActive: true
      });
      console.log('✅ Admin account created');
    } else {
      console.log('ℹ️  Admin account already exists');
    }

    // Create student account
    const studentPassword = 'student123';
    const hashedStudentPassword = await bcrypt.hash(studentPassword, 10);

    const studentExists = await Student.findOne({ email: '<EMAIL>' });

    if (!studentExists) {
      const student = await Student.create({
        name: 'John Doe',
        email: '<EMAIL>',
        password: hashedStudentPassword,
        profilePicture: '/img/default-avatar.png',
        isActive: true
      });
      console.log('✅ Student account created');

      // Create sample courses
      const courses = [
        {
          title: 'HTML & CSS Fundamentals',
          description: 'Master the fundamentals of web development with HTML and CSS.',
          badgeImage: '/img/htmlbadge.svg',
          active: true
        },
        {
          title: 'JavaScript Programming',
          description: 'Learn modern JavaScript programming and ES6+ features.',
          badgeImage: '/img/jsbadge.svg',
          active: true
        },
        {
          title: 'React.js Development',
          description: 'Build modern web applications with React.js.',
          badgeImage: '/img/reactbadge.svg',
          active: true
        }
      ];

      const createdCourses = await Course.insertMany(courses);
      console.log('✅ Sample courses created');

      // Create sample certificates for the student
      const certificates = createdCourses.map(course => ({
        studentId: student._id,
        courseId: course._id,
        issueDate: new Date(),
        certificateId: randomUUID(),
        downloadCount: Math.floor(Math.random() * 10),
        shareCount: Math.floor(Math.random() * 5),
        isRevoked: false
      }));

      await Certificate.insertMany(certificates);
      console.log('✅ Sample certificates created');

    } else {
      console.log('ℹ️  Student account already exists');
    }

    console.log('\n🎉 Test accounts and data created successfully!');
    console.log('📧 Admin login: email=<EMAIL>, password=admin123');
    console.log('📧 Student login: email=<EMAIL>, password=student123');

  } catch (error) {
    console.error('❌ Error creating test accounts:', error);
  } finally {
    process.exit(0);
  }
}

createTestAccounts();