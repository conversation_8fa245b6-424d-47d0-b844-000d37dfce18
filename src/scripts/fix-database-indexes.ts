import dbConnect from '@/lib/db/connection';
import { Certificate } from '@/lib/db/models';

async function fixDatabaseIndexes() {
  try {
    console.log('🔧 Fixing database indexes...');
    
    await dbConnect();
    console.log('✅ Connected to database');

    // Get the collection
    const collection = Certificate.collection;

    // Drop the existing certificateId index if it exists
    try {
      await collection.dropIndex('certificateId_1');
      console.log('✅ Dropped existing certificateId index');
    } catch (error) {
      console.log('ℹ️  certificateId index does not exist or already dropped');
    }

    // Create a new sparse unique index for certificateId
    await collection.createIndex(
      { certificateId: 1 }, 
      { 
        unique: true, 
        sparse: true, // This allows multiple null values
        name: 'certificateId_sparse_unique'
      }
    );
    console.log('✅ Created new sparse unique index for certificateId');

    // List all indexes to verify
    const indexes = await collection.listIndexes().toArray();
    console.log('\n📋 Current indexes:');
    indexes.forEach(index => {
      console.log(`   - ${index.name}: ${JSON.stringify(index.key)} ${index.unique ? '(unique)' : ''} ${index.sparse ? '(sparse)' : ''}`);
    });

    console.log('\n🎉 Database indexes fixed successfully!');
    console.log('You can now run the migration script again.');

  } catch (error) {
    console.error('❌ Error fixing database indexes:', error);
  } finally {
    process.exit(0);
  }
}

fixDatabaseIndexes();
