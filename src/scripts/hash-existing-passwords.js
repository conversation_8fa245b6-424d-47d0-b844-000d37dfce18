const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');

// MongoDB connection string - update this with your actual connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/certificate-system';

// Student schema (simplified for migration)
const studentSchema = new mongoose.Schema({
  name: String,
  email: String,
  password: String,
  role: String,
  isActive: Boolean,
  joinDate: Date,
  courses: Array,
  createdAt: Date,
  updatedAt: Date
});

const Student = mongoose.model('Student', studentSchema);

// Admin schema (simplified for migration)
const adminSchema = new mongoose.Schema({
  name: String,
  email: String,
  username: String,
  password: String,
  role: String,
  permissions: Array,
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
});

const Admin = mongoose.model('Admin', adminSchema);

async function hashExistingPasswords() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');

    // Check if passwords are already hashed (bcrypt hashes start with $2a$, $2b$, or $2y$)
    const isPasswordHashed = (password) => {
      return password && (password.startsWith('$2a$') || password.startsWith('$2b$') || password.startsWith('$2y$'));
    };

    // Hash student passwords
    console.log('Checking student passwords...');
    const students = await Student.find({});
    let studentsUpdated = 0;

    for (const student of students) {
      if (student.password && !isPasswordHashed(student.password)) {
        console.log(`Hashing password for student: ${student.email}`);
        const hashedPassword = await bcrypt.hash(student.password, 12);
        await Student.findByIdAndUpdate(student._id, { password: hashedPassword });
        studentsUpdated++;
      }
    }

    console.log(`Updated ${studentsUpdated} student passwords`);

    // Hash admin passwords
    console.log('Checking admin passwords...');
    const admins = await Admin.find({});
    let adminsUpdated = 0;

    for (const admin of admins) {
      if (admin.password && !isPasswordHashed(admin.password)) {
        console.log(`Hashing password for admin: ${admin.email}`);
        const hashedPassword = await bcrypt.hash(admin.password, 12);
        await Admin.findByIdAndUpdate(admin._id, { password: hashedPassword });
        adminsUpdated++;
      }
    }

    console.log(`Updated ${adminsUpdated} admin passwords`);
    console.log('Password hashing migration completed successfully!');

  } catch (error) {
    console.error('Error during password hashing migration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  hashExistingPasswords();
}

module.exports = { hashExistingPasswords };
