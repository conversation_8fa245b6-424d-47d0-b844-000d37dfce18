import dbConnect from '@/lib/db/connection';
import { Certificate, Student, Course } from '@/lib/db/models';
import bcrypt from 'bcryptjs';
import fs from 'fs';
import path from 'path';

interface LegacyCourseData {
  courseTitle: string;
  date: string;
  _id: {
    $oid: string;
  };
}

interface LegacyStudentData {
  _id: {
    $oid: string;
  };
  name: string;
  email: string;
  course: LegacyCourseData[];
  date: string;
  __v: number;
}

// Course mapping for better descriptions and durations
const COURSE_MAPPING: Record<string, { description: string; duration: string; badgeImage: string }> = {
  'HTML': {
    description: 'Learn the fundamentals of HTML markup language for web development',
    duration: '4 weeks',
    badgeImage: '/badges/html.png'
  },
  'CSS': {
    description: 'Master CSS styling and responsive design techniques',
    duration: '4 weeks',
    badgeImage: '/badges/css.png'
  },
  'JAVASCRIPT': {
    description: 'Learn JavaScript programming for interactive web applications',
    duration: '6 weeks',
    badgeImage: '/badges/javascript.png'
  },
  'REACTJS': {
    description: 'Build modern web applications with React.js framework',
    duration: '8 weeks',
    badgeImage: '/badges/reactjs.png'
  },
  'NODEJS': {
    description: 'Server-side JavaScript development with Node.js',
    duration: '6 weeks',
    badgeImage: '/badges/nodejs.png'
  },
  'PYTHON': {
    description: 'Learn Python programming language fundamentals',
    duration: '8 weeks',
    badgeImage: '/badges/python.png'
  },
  'PYTHON FUNDAMENTALS': {
    description: 'Master the basics of Python programming',
    duration: '6 weeks',
    badgeImage: '/badges/python.png'
  },
  'GITHUB': {
    description: 'Version control and collaboration with Git and GitHub',
    duration: '2 weeks',
    badgeImage: '/badges/github.png'
  },
  'BOOTSTRAP': {
    description: 'Responsive web design with Bootstrap framework',
    duration: '3 weeks',
    badgeImage: '/badges/bootstrap.png'
  },
  'CYBER SECURITY': {
    description: 'Fundamentals of cybersecurity and digital protection',
    duration: '6 weeks',
    badgeImage: '/badges/cybersecurity.png'
  },
  'INTERNSHIP': {
    description: 'Practical work experience and professional development',
    duration: '12 weeks',
    badgeImage: '/badges/internship.png'
  },
  'BACKEND JAVASCRIPT': {
    description: 'Backend development with JavaScript and Node.js',
    duration: '8 weeks',
    badgeImage: '/badges/backend-js.png'
  }
};

// Function to parse custom date formats like "September 12th 2022"
function parseCustomDate(dateString: string): Date {
  try {
    // Remove ordinal suffixes (st, nd, rd, th) from the date string
    const cleanedDate = dateString.replace(/(\d+)(st|nd|rd|th)/g, '$1');

    // Try to parse the cleaned date
    const parsedDate = new Date(cleanedDate);

    // Check if the date is valid
    if (!isNaN(parsedDate.getTime())) {
      return parsedDate;
    }

    // If that fails, try manual parsing for common formats
    const monthMap: Record<string, number> = {
      'January': 0, 'February': 1, 'March': 2, 'April': 3,
      'May': 4, 'June': 5, 'July': 6, 'August': 7,
      'September': 8, 'October': 9, 'November': 10, 'December': 11
    };

    // Match pattern like "September 12th 2022"
    const match = dateString.match(/(\w+)\s+(\d+)(?:st|nd|rd|th)?\s+(\d{4})/);
    if (match) {
      const [, monthName, day, year] = match;
      const monthIndex = monthMap[monthName];

      if (monthIndex !== undefined) {
        return new Date(parseInt(year), monthIndex, parseInt(day));
      }
    }

    // If all parsing fails, return a default date
    console.warn(`⚠️  Could not parse date: "${dateString}", using current date`);
    return new Date();

  } catch (error) {
    console.warn(`⚠️  Error parsing date: "${dateString}", using current date`);
    return new Date();
  }
}

async function migrateLegacyCertificates() {
  try {
    console.log('🚀 Starting legacy certificate migration...');

    // Connect to the database
    await dbConnect();
    console.log('✅ Connected to database');

    // Load the JSON data
    const jsonPath = path.join(process.cwd(), 'certDB.certifiedusers.json');

    if (!fs.existsSync(jsonPath)) {
      console.error('❌ JSON file not found at:', jsonPath);
      console.log('📝 Please ensure certDB.certifiedusers.json is in the project root');
      return;
    }

    const jsonData = fs.readFileSync(jsonPath, 'utf8');
    const legacyStudents: LegacyStudentData[] = JSON.parse(jsonData);

    console.log(`📊 Found ${legacyStudents.length} legacy students to migrate`);

    // Extract all unique courses from the data
    const uniqueCourses = new Set<string>();
    let totalCertificates = 0;

    legacyStudents.forEach(student => {
      student.course.forEach(course => {
        uniqueCourses.add(course.courseTitle);
        totalCertificates++;
      });
    });

    console.log(`📚 Found ${uniqueCourses.size} unique courses`);
    console.log(`📜 Total certificates to migrate: ${totalCertificates}`);

    // Step 1: Create all unique courses
    console.log('\n📚 Creating courses...');
    const courseMap = new Map<string, string>(); // courseTitle -> courseId

    for (const courseTitle of uniqueCourses) {
      try {
        // Check if course already exists
        let course = await Course.findOne({ title: courseTitle });

        if (!course) {
          const courseInfo = COURSE_MAPPING[courseTitle] || {
            description: `${courseTitle} course - description to be updated`,
            duration: '4 weeks',
            badgeImage: '/badges/default.png'
          };

          course = new Course({
            title: courseTitle,
            description: courseInfo.description,
            duration: courseInfo.duration,
            badgeImage: courseInfo.badgeImage,
            active: true
          });

          await course.save();
          console.log(`✅ Created course: ${courseTitle}`);
        } else {
          console.log(`⏭️  Course already exists: ${courseTitle}`);
        }

        courseMap.set(courseTitle, course._id.toString());
      } catch (error) {
        console.error(`❌ Error creating course ${courseTitle}:`, error);
      }
    }

    // Step 2: Process students and certificates
    console.log('\n👥 Processing students and certificates...');
    let migratedStudents = 0;
    let migratedCertificates = 0;
    let skippedCount = 0;
    let errorCount = 0;
    let processedCount = 0;

    for (const legacyStudent of legacyStudents) {
      processedCount++;
      try {
        console.log(`\n🔄 Processing student: ${legacyStudent.name} (${legacyStudent.email})`);

        // Find or create student
        let student = await Student.findOne({ email: legacyStudent.email });
        let isNewStudent = false;

        if (!student) {
          console.log(`👤 Creating new student: ${legacyStudent.email}`);

          // Hash the standard password
          const hashedPassword = await bcrypt.hash('CapacityBay@123', 12);

          student = new Student({
            name: legacyStudent.name,
            email: legacyStudent.email,
            password: hashedPassword,
            isActive: true,
            courses: []
          });

          await student.save();
          migratedStudents++;
          isNewStudent = true;
          console.log(`✅ Created student: ${legacyStudent.name}`);
        } else {
          console.log(`⏭️  Student already exists: ${legacyStudent.email}`);
        }

        // Process each certificate for this student
        for (const legacyCourse of legacyStudent.course) {
          try {
            const certificateId = legacyCourse._id.$oid;
            console.log(`  📜 Processing certificate: ${certificateId} for course: ${legacyCourse.courseTitle}`);

            // Check if certificate already exists (avoid duplicates)
            const existingCert = await Certificate.findById(certificateId);
            if (existingCert) {
              console.log(`    ⏭️  Certificate ${certificateId} already exists, skipping...`);
              skippedCount++;
              continue;
            }

            // Get course ID from our course map
            const courseId = courseMap.get(legacyCourse.courseTitle);
            if (!courseId) {
              console.error(`    ❌ Course not found: ${legacyCourse.courseTitle}`);
              errorCount++;
              continue;
            }

            // Parse the date (convert from format like "September 12th 2022" to Date)
            const issueDate = parseCustomDate(legacyCourse.date);
            console.log(`    📅 Parsed date "${legacyCourse.date}" to: ${issueDate.toISOString()}`);

            // Create legacy certificate using the original MongoDB ObjectId
            const certificate = new Certificate({
              _id: certificateId, // Use the original MongoDB ObjectId
              studentId: student._id,
              courseId: courseId,
              issueDate: issueDate,
              // Don't set certificateId for legacy certificates (they use _id)
              downloadCount: 0,
              shareCount: 0,
              isRevoked: false,
              isLegacy: true
            });

            // Validate the certificate before saving
            const validationError = certificate.validateSync();
            if (validationError) {
              console.error(`    ❌ Certificate validation failed:`, validationError.message);
              errorCount++;
              continue;
            }

            await certificate.save();

            // Add course to student's courses array if not already present
            if (!student.courses.includes(courseId)) {
              student.courses.push(courseId);
            }

            console.log(`    ✅ Migrated certificate: ${certificateId}`);
            migratedCertificates++;

          } catch (certError) {
            console.error(`    ❌ Error migrating certificate ${legacyCourse._id.$oid}:`);
            console.error(`    📝 Error details:`, (certError as Error).message);
            console.error(`    📅 Date string: "${legacyCourse.date}"`);
            console.error(`    📚 Course: ${legacyCourse.courseTitle}`);
            errorCount++;
          }
        }

        // Save student with updated courses
        if (isNewStudent || student.courses.length > 0) {
          await student.save();
        }

      } catch (error) {
        console.error(`❌ Error processing student ${legacyStudent.name}:`, error);
        errorCount++;
      }
    }

    console.log('\n🎉 Migration completed!');
    console.log(`📊 Summary:`);
    console.log(`   👥 Students: ${migratedStudents} created`);
    console.log(`   📚 Courses: ${uniqueCourses.size} processed`);
    console.log(`   ✅ Certificates: ${migratedCertificates} migrated`);
    console.log(`   ⏭️  Skipped: ${skippedCount} certificates (already exist)`);
    console.log(`   ❌ Errors: ${errorCount} certificates`);

    if (migratedCertificates > 0) {
      console.log('\n📝 Next steps:');
      console.log('1. Test verification with legacy URLs like: /verify/631f854a9b27e97f9ebce5b5');
      console.log('2. Students can login with email and password: CapacityBay@123');
      console.log('3. Update course descriptions and badge images as needed');
      console.log('4. Test certificate downloads with legacy IDs');
      console.log('5. Test root-level URLs like: http://localhost:3000/631f854a9b27e97f9ebce5b5');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    process.exit(0);
  }
}

// Instructions for using this script:
console.log(`
📋 LEGACY CERTIFICATE MIGRATION INSTRUCTIONS:

1. Ensure certDB.certifiedusers.json is in the project root directory
2. The script will automatically:
   - Create all unique courses found in the data
   - Create student accounts with email and password: CapacityBay@123
   - Migrate all certificates with original MongoDB ObjectIds
   - Maintain backward compatibility for old verification URLs

3. Run the migration:
   npm run migrate-legacy

4. Test the migration by visiting legacy URLs like:
   - http://localhost:3000/verify/631f854a9b27e97f9ebce5b5
   - http://localhost:3000/631f854a9b27e97f9ebce5b5 (root-level URL)

✅ FEATURES:
- All students get password: CapacityBay@123
- Original certificate IDs are preserved
- Courses are automatically created with proper descriptions
- Both /verify/[id] and root-level /[id] URLs work
- Legacy certificates work seamlessly with new system
`);

migrateLegacyCertificates();
