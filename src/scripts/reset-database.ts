import dbConnect from '../lib/db/connection';
import { Admin, Student, Course, Certificate } from '../lib/db/models';

async function resetDatabase() {
  try {
    // Connect to the database
    await dbConnect();
    console.log('Connected to database');

    // Clear all collections
    await Admin.deleteMany({});
    console.log('✅ Cleared admins collection');

    await Student.deleteMany({});
    console.log('✅ Cleared students collection');

    await Course.deleteMany({});
    console.log('✅ Cleared courses collection');

    await Certificate.deleteMany({});
    console.log('✅ Cleared certificates collection');

    console.log('\n🎉 Database reset successfully!');
    console.log('You can now run: npm run create-accounts');

  } catch (error) {
    console.error('❌ Error resetting database:', error);
  } finally {
    process.exit(0);
  }
}

resetDatabase();
