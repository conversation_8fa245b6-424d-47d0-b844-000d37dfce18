import dbConnect from '../lib/db/connection';
import { Student, Course, Certificate } from '../lib/db/models';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

async function seedDatabase() {
  try {
    await dbConnect();
    console.log('Connected to database');

    // Clear existing data
    await Student.deleteMany({});
    await Course.deleteMany({});
    await Certificate.deleteMany({});
    console.log('Cleared existing data');

    // Create sample courses
    const courses = [
      {
        title: 'Full Stack Web Development',
        description: 'Complete course covering HTML, CSS, JavaScript, React, Node.js, and MongoDB. Learn to build modern web applications from scratch.',
        badgeImage: '/img/fullstackbadge.svg',
        certificateTemplate: 'default',
        active: true
      },
      {
        title: 'React.js Development',
        description: 'Master React.js and build dynamic user interfaces. Covers components, hooks, state management, and modern React patterns.',
        badgeImage: '/img/reactbadge.svg',
        certificateTemplate: 'default',
        active: true
      },
      {
        title: 'JavaScript Fundamentals',
        description: 'Learn JavaScript from basics to advanced concepts. Covers ES6+, async programming, DOM manipulation, and modern JavaScript.',
        badgeImage: '/img/jsbadge.svg',
        certificateTemplate: 'default',
        active: true
      },
      {
        title: 'HTML & CSS Mastery',
        description: 'Master HTML5 and CSS3 with responsive design, flexbox, grid, and modern web standards.',
        badgeImage: '/img/htmlbadge.svg',
        certificateTemplate: 'default',
        active: true
      },
      {
        title: 'Python Programming',
        description: 'Learn Python programming from basics to advanced topics. Covers data structures, OOP, and popular frameworks.',
        badgeImage: '/img/jsbadge.svg',
        certificateTemplate: 'default',
        active: true
      }
    ];

    const createdCourses = await Course.insertMany(courses);
    console.log(`Created ${createdCourses.length} courses`);

    // Create sample students
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    const students = [
      {
        name: 'John Doe',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'student',
        courses: []
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'student',
        courses: []
      },
      {
        name: 'Mike Johnson',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'student',
        courses: []
      },
      {
        name: 'Sarah Wilson',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'student',
        courses: []
      },
      {
        name: 'David Brown',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'student',
        courses: []
      },
      {
        name: 'Emily Davis',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'student',
        courses: []
      }
    ];

    const createdStudents = await Student.insertMany(students);
    console.log(`Created ${createdStudents.length} students`);

    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 12);
    const admin = new Student({
      name: 'Admin User',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'admin',
      courses: []
    });
    await admin.save();
    console.log('Created admin user');

    // Create sample certificates
    const certificates: any[] = [];
    
    // Issue certificates for some students
    for (let i = 0; i < 8; i++) {
      const student = createdStudents[Math.floor(Math.random() * createdStudents.length)];
      const course = createdCourses[Math.floor(Math.random() * createdCourses.length)];
      
      // Check if certificate already exists for this student-course combination
      const existingCert = certificates.find(cert => 
        cert.studentId.toString() === student._id.toString() && 
        cert.courseId.toString() === course._id.toString()
      );
      
      if (!existingCert) {
        const certificate = {
          studentId: student._id,
          courseId: course._id,
          issueDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000), // Random date within last 90 days
          certificateId: uuidv4(),
          downloadCount: Math.floor(Math.random() * 10),
          shareCount: Math.floor(Math.random() * 5),
          isRevoked: Math.random() < 0.1 // 10% chance of being revoked
        };
        
        certificates.push(certificate);
        
        // Add course to student's courses array if not already present
        if (!student.courses || !student.courses.includes(course._id)) {
          if (!student.courses) student.courses = [];
          student.courses.push(course._id);
          await student.save();
        }
      }
    }

    const createdCertificates = await Certificate.insertMany(certificates);
    console.log(`Created ${createdCertificates.length} certificates`);

    console.log('\n=== Database Seeded Successfully ===');
    console.log('\nTest Accounts:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Student: <EMAIL> / password123');
    console.log('Student: <EMAIL> / password123');
    console.log('\nSample Data:');
    console.log(`- ${createdCourses.length} courses`);
    console.log(`- ${createdStudents.length} students`);
    console.log(`- ${createdCertificates.length} certificates`);
    console.log(`- 1 admin user`);

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    process.exit(0);
  }
}

// Run the seed function
seedDatabase();
