// Test script to verify date parsing works correctly

// Function to parse custom date formats like "September 12th 2022"
function parseCustomDate(dateString: string): Date {
  try {
    // Remove ordinal suffixes (st, nd, rd, th) from the date string
    const cleanedDate = dateString.replace(/(\d+)(st|nd|rd|th)/g, '$1');
    
    // Try to parse the cleaned date
    const parsedDate = new Date(cleanedDate);
    
    // Check if the date is valid
    if (!isNaN(parsedDate.getTime())) {
      return parsedDate;
    }
    
    // If that fails, try manual parsing for common formats
    const monthMap: Record<string, number> = {
      'January': 0, 'February': 1, 'March': 2, 'April': 3,
      'May': 4, 'June': 5, 'July': 6, 'August': 7,
      'September': 8, 'October': 9, 'November': 10, 'December': 11
    };
    
    // Match pattern like "September 12th 2022"
    const match = dateString.match(/(\w+)\s+(\d+)(?:st|nd|rd|th)?\s+(\d{4})/);
    if (match) {
      const [, monthName, day, year] = match;
      const monthIndex = monthMap[monthName];
      
      if (monthIndex !== undefined) {
        return new Date(parseInt(year), monthIndex, parseInt(day));
      }
    }
    
    // If all parsing fails, return a default date
    console.warn(`⚠️  Could not parse date: "${dateString}", using current date`);
    return new Date();
    
  } catch (error) {
    console.warn(`⚠️  Error parsing date: "${dateString}", using current date`);
    return new Date();
  }
}

// Test the date parsing function
function testDateParsing() {
  console.log('🧪 Testing date parsing function...\n');
  
  const testDates = [
    "September 12th 2022",
    "December 20th 2022",
    "January 9th 2024",
    "January 22nd 2023",
    "December 21st 2022",
    "January 13th 2023",
    "December 18th 2023",
    "December 19th 2023",
    "May 12th 2020",
    "February 11th 2022"
  ];
  
  testDates.forEach(dateString => {
    const parsed = parseCustomDate(dateString);
    console.log(`📅 "${dateString}" → ${parsed.toISOString()} (${parsed.toDateString()})`);
  });
  
  console.log('\n✅ Date parsing test completed!');
}

testDateParsing();
