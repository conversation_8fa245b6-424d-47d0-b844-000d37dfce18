import dbConnect from '@/lib/db/connection';
import { Certificate, Student, Course } from '@/lib/db/models';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

async function createTestCertificates() {
  try {
    console.log('🧪 Creating test certificates for backward compatibility testing...');
    
    await dbConnect();
    console.log('✅ Connected to database');

    // Create test student
    const hashedPassword = await bcrypt.hash('student123', 12);
    const testStudent = new Student({
      name: 'Test Student',
      email: '<EMAIL>',
      password: hashedPassword,
      isActive: true,
      courses: []
    });
    await testStudent.save();

    // Create test course
    const testCourse = new Course({
      title: 'Test Course for Legacy Verification',
      description: 'A test course to verify backward compatibility',
      duration: '4 weeks',
      badgeImage: '/badges/test.png',
      active: true
    });
    await testCourse.save();

    // Create a legacy certificate (with real MongoDB ObjectId from your data)
    const legacyObjectId = '631f854a9b27e97f9ebce5b5'; // <PERSON>'s student ID
    const legacyCertificate = new Certificate({
      _id: legacyObjectId,
      studentId: testStudent._id,
      courseId: testCourse._id,
      issueDate: new Date('2022-09-12'),
      // No certificateId for legacy certificate
      downloadCount: 5,
      shareCount: 2,
      isRevoked: false,
      isLegacy: true
    });
    await legacyCertificate.save();

    // Create another legacy certificate (with real certificate ID from your data)
    const legacyCertId = '631f854a9b27e97f9ebce5b3'; // Franklin Azodo's HTML certificate
    const legacyCertificate2 = new Certificate({
      _id: legacyCertId,
      studentId: testStudent._id,
      courseId: testCourse._id,
      issueDate: new Date('2022-09-12'),
      downloadCount: 3,
      shareCount: 1,
      isRevoked: false,
      isLegacy: true
    });
    await legacyCertificate2.save();

    // Create a 32-char hex legacy certificate (for testing)
    const legacy32CharId = 'eacbebc81e1e4c96829b185021488352';
    const legacy32Certificate = new Certificate({
      studentId: testStudent._id,
      courseId: testCourse._id,
      issueDate: new Date('2023-12-01'),
      certificateId: legacy32CharId, // Use as certificateId for 32-char hex
      downloadCount: 3,
      shareCount: 1,
      isRevoked: false,
      isLegacy: true
    });
    await legacy32Certificate.save();

    // Create a new certificate (with UUID)
    const newCertificateId = uuidv4();
    const newCertificate = new Certificate({
      studentId: testStudent._id,
      courseId: testCourse._id,
      issueDate: new Date(),
      certificateId: newCertificateId,
      downloadCount: 0,
      shareCount: 0,
      isRevoked: false,
      isLegacy: false
    });
    await newCertificate.save();

    console.log('✅ Test certificates created successfully!');
    console.log('\n📋 Test URLs:');
    console.log(`🔗 Legacy certificate (student ID): http://localhost:3000/verify/${legacyObjectId}`);
    console.log(`🔗 Legacy certificate (cert ID): http://localhost:3000/verify/${legacyCertId}`);
    console.log(`🔗 Legacy certificate (32-char): http://localhost:3000/verify/${legacy32CharId}`);
    console.log(`🔗 New certificate: http://localhost:3000/verify/${newCertificateId}`);
    console.log('\n📋 Root-level URLs (old format):');
    console.log(`🔗 Legacy root URL (student): http://localhost:3000/${legacyObjectId}`);
    console.log(`🔗 Legacy root URL (cert): http://localhost:3000/${legacyCertId}`);
    console.log(`🔗 Legacy root URL (32-char): http://localhost:3000/${legacy32CharId}`);
    console.log('\n📋 Download URLs:');
    console.log(`📥 Legacy download (student): http://localhost:3000/api/certificates/download/${legacyObjectId}`);
    console.log(`📥 Legacy download (cert): http://localhost:3000/api/certificates/download/${legacyCertId}`);
    console.log(`📥 Legacy download (32-char): http://localhost:3000/api/certificates/download/${legacy32CharId}`);
    console.log(`📥 New download: http://localhost:3000/api/certificates/download/${newCertificateId}`);
    
    console.log('\n🧪 Testing verification API...');
    
    // Test legacy verification (student ID)
    console.log(`\n🔍 Testing legacy certificate verification (student ID)...`);
    const legacyResponse = await fetch(`http://localhost:3000/api/verify/${legacyObjectId}`);
    if (legacyResponse.ok) {
      const legacyData = await legacyResponse.json();
      console.log('✅ Legacy verification (student ID) successful');
      console.log(`   Student: ${legacyData.student.name}`);
      console.log(`   Course: ${legacyData.course.title}`);
      console.log(`   Is Legacy: ${legacyData.isLegacy}`);
    } else {
      console.log('❌ Legacy verification (student ID) failed');
    }

    // Test legacy verification (certificate ID)
    console.log(`\n🔍 Testing legacy certificate verification (cert ID)...`);
    const legacyCertResponse = await fetch(`http://localhost:3000/api/verify/${legacyCertId}`);
    if (legacyCertResponse.ok) {
      const legacyCertData = await legacyCertResponse.json();
      console.log('✅ Legacy verification (cert ID) successful');
      console.log(`   Student: ${legacyCertData.student.name}`);
      console.log(`   Course: ${legacyCertData.course.title}`);
      console.log(`   Is Legacy: ${legacyCertData.isLegacy}`);
    } else {
      console.log('❌ Legacy verification (cert ID) failed');
    }

    // Test legacy verification (32-char)
    console.log(`\n🔍 Testing legacy certificate verification (32-char)...`);
    const legacy32Response = await fetch(`http://localhost:3000/api/verify/${legacy32CharId}`);
    if (legacy32Response.ok) {
      const legacy32Data = await legacy32Response.json();
      console.log('✅ Legacy verification (32-char) successful');
      console.log(`   Student: ${legacy32Data.student.name}`);
      console.log(`   Course: ${legacy32Data.course.title}`);
      console.log(`   Is Legacy: ${legacy32Data.isLegacy}`);
    } else {
      console.log('❌ Legacy verification (32-char) failed');
    }

    // Test new verification
    console.log(`\n🔍 Testing new certificate verification...`);
    const newResponse = await fetch(`http://localhost:3000/api/verify/${newCertificateId}`);
    if (newResponse.ok) {
      const newData = await newResponse.json();
      console.log('✅ New verification successful');
      console.log(`   Student: ${newData.student.name}`);
      console.log(`   Course: ${newData.course.title}`);
      console.log(`   Is Legacy: ${newData.isLegacy}`);
    } else {
      console.log('❌ New verification failed');
    }

    console.log('\n🎉 Test setup completed!');
    console.log('\n📝 Next steps:');
    console.log('1. Start your development server: npm run dev');
    console.log('2. Test the URLs above in your browser');
    console.log('3. Verify that both legacy formats (24-char and 32-char) work');
    console.log('4. Test root-level URLs (old format) redirect properly');
    console.log('5. Test certificate downloads for all formats');
    console.log('6. Verify that new UUID certificates still work');

  } catch (error) {
    console.error('❌ Error creating test certificates:', error);
  } finally {
    process.exit(0);
  }
}

createTestCertificates();
