// Test browser detection for certificate generation
const { VercelPuppeteerPDFGenerator } = require('./src/lib/pdf/vercel-puppeteer-pdf-generator');

async function testBrowserDetection() {
  console.log('Testing browser detection and certificate generation...');
  console.log('Environment variables:');
  console.log('CHROMIUM_REMOTE_EXEC_PATH:', process.env.CHROMIUM_REMOTE_EXEC_PATH || 'Not set');
  console.log('CHROMIUM_LOCAL_EXEC_PATH:', process.env.CHROMIUM_LOCAL_EXEC_PATH || 'Not set');
  
  try {
    const generator = new VercelPuppeteerPDFGenerator();
    
    // Mock certificate data
    const mockData = {
      certificate: {
        certificateId: 'test-cert-123',
        issueDate: new Date(),
        _id: 'mock-id'
      },
      student: {
        name: '<PERSON>',
        email: '<EMAIL>'
      },
      course: {
        title: 'Test Course',
        description: 'A test course for certificate generation'
      }
    };
    
    console.log('Generating PDF...');
    const pdfBuffer = await generator.generateCertificate(mockData);
    
    console.log(`✅ Success! Generated PDF with ${pdfBuffer.length} bytes`);
    console.log('Browser detection and certificate generation test completed successfully.');
    
  } catch (error) {
    console.error('❌ Error generating certificate:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the test
testBrowserDetection();
