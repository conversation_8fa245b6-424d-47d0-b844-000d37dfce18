const { HTMLToPDFGenerator } = require('./src/lib/pdf/html-to-pdf-generator');

// Test certificate generation with mock data
async function testCertificateGeneration() {
  console.log('Testing certificate generation...');
  
  try {
    const generator = new HTMLToPDFGenerator();
    
    // Mock certificate data
    const mockData = {
      certificate: {
        certificateId: 'test-cert-123',
        issueDate: new Date(),
        _id: 'mock-id'
      },
      student: {
        name: '<PERSON>',
        email: '<EMAIL>'
      },
      course: {
        title: 'Test Course',
        description: 'A test course for certificate generation'
      }
    };
    
    console.log('Generating PDF...');
    const pdfBuffer = await generator.generateCertificate(mockData);
    
    console.log(`✅ Success! Generated PDF with ${pdfBuffer.length} bytes`);
    console.log('Certificate generation test completed successfully.');
    
  } catch (error) {
    console.error('❌ Error generating certificate:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

// Run the test
testCertificateGeneration();
