// Test certificate generation
import { CertificateGenerator } from './src/lib/pdf/certificate-generator';
import * as fs from 'fs';

async function testCertificate() {
  try {
    const generator = new CertificateGenerator();

    const testData = {
      certificate: {
        certificateId: 'test-cert-123',
        issueDate: new Date(),
        downloadCount: 0,
        shareCount: 0,
        isRevoked: false
      },
      student: {
        name: '<PERSON>',
        email: '<EMAIL>'
      },
      course: {
        title: 'Web Development Fundamentals',
        description: 'Learn the basics of web development'
      }
    };

    console.log('Generating certificate...');
    const pdfBuffer = await generator.generateCertificate(testData);

    console.log('Certificate generated successfully!');
    console.log('PDF Buffer size:', pdfBuffer.length, 'bytes');

    // Save to file for testing
    fs.writeFileSync('test-certificate.pdf', pdfBuffer);
    console.log('Certificate saved as test-certificate.pdf');

  } catch (error) {
    console.error('Error generating certificate:', error);
  }
}

testCertificate();
